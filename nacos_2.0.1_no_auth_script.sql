-- 若依系统Nacos 2.0.1无认证模板
-- 使用方法：将此SQL导入到若依系统数据库中

-- 检查是否存在同名模板，如果存在则删除
DELETE FROM sys_docker_template WHERE template_name = 'Nacos配置中心(2.0.1无认证)';

-- 插入Nacos 2.0.1无认证模板
INSERT INTO sys_docker_template (
    template_id, template_name, template_description, image_name, image_tag, 
    port_mappings, volume_mappings, environment_vars, command, 
    network_mode, restart_policy, is_system, create_by, create_time, remark
) VALUES (
    (SELECT IFNULL(MAX(template_id), 0) + 1 FROM sys_docker_template t), 
    'Nacos配置中心(2.0.1无认证)', 
    'Nacos 是阿里巴巴推出来的一个新开源项目，是一个更易于构建云原生应用的动态服务发现、配置管理和服务管理平台', 
    'nacos/nacos-server', 
    '2.0.1', 
    '8848:8848,9848:9848,9849:9849', 
    '/data/nacos/logs:/home/<USER>/logs,/data/nacos/data:/home/<USER>/data', 
    'MODE=standalone,PREFER_HOST_MODE=hostname,NACOS_AUTH_ENABLE=false,JVM_XMS=512m,JVM_XMX=512m,JVM_XMN=256m', 
    '', 
    'bridge', 
    'always', 
    '1', 
    'admin', 
    NOW(), 
    '部署前准备：
1. 创建数据目录：mkdir -p /data/nacos/logs /data/nacos/data
2. 设置目录权限：chmod -R 777 /data/nacos

部署后访问：
http://服务器IP:8848/nacos
用户名/密码：nacos/nacos (无认证模式下不需要登录)

注意事项：
1. 此模板使用Nacos 2.0.1版本，无需配置数据库，使用内嵌数据库
2. 无认证模式下，任何人都可以访问Nacos控制台和API
3. 生产环境建议开启认证和配置外部MySQL数据库
4. 如需配置外部MySQL数据库，请添加以下环境变量：
   SPRING_DATASOURCE_PLATFORM=mysql
   MYSQL_SERVICE_HOST=数据库IP
   MYSQL_SERVICE_PORT=3306
   MYSQL_SERVICE_DB_NAME=nacos
   MYSQL_SERVICE_USER=数据库用户名
   MYSQL_SERVICE_PASSWORD=数据库密码'
);

-- 部署命令参考（若依系统会自动生成类似命令）
/*
docker run -d --name nacos --network bridge --restart always \
  -p 8848:8848 -p 9848:9848 -p 9849:9849 \
  -v /data/nacos/logs:/home/<USER>/logs \
  -v /data/nacos/data:/home/<USER>/data \
  -e MODE=standalone \
  -e PREFER_HOST_MODE=hostname \
  -e NACOS_AUTH_ENABLE=false \
  -e JVM_XMS=512m \
  -e JVM_XMX=512m \
  -e JVM_XMN=256m \
  nacos/nacos-server:2.0.1
*/ 