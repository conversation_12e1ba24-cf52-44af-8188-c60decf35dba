package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.datasync.SyncFileSyncLog;

/**
 * 文件同步日志Mapper接口
 * 
 * <AUTHOR>
 */
public interface SyncFileSyncLogMapper 
{
    /**
     * 查询文件同步日志
     * 
     * @param logId 文件同步日志主键
     * @return 文件同步日志
     */
    public SyncFileSyncLog selectSyncFileSyncLogByLogId(Long logId);

    /**
     * 查询文件同步日志列表
     * 
     * @param syncFileSyncLog 文件同步日志
     * @return 文件同步日志集合
     */
    public List<SyncFileSyncLog> selectSyncFileSyncLogList(SyncFileSyncLog syncFileSyncLog);

    /**
     * 根据任务ID查询文件同步日志列表
     * 
     * @param taskId 文件同步任务ID
     * @return 文件同步日志集合
     */
    public List<SyncFileSyncLog> selectSyncFileSyncLogByTaskId(Long taskId);

    /**
     * 新增文件同步日志
     * 
     * @param syncFileSyncLog 文件同步日志
     * @return 结果
     */
    public int insertSyncFileSyncLog(SyncFileSyncLog syncFileSyncLog);

    /**
     * 修改文件同步日志
     * 
     * @param syncFileSyncLog 文件同步日志
     * @return 结果
     */
    public int updateSyncFileSyncLog(SyncFileSyncLog syncFileSyncLog);

    /**
     * 删除文件同步日志
     * 
     * @param logId 文件同步日志主键
     * @return 结果
     */
    public int deleteSyncFileSyncLogByLogId(Long logId);

    /**
     * 批量删除文件同步日志
     * 
     * @param logIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSyncFileSyncLogByLogIds(Long[] logIds);
    
    /**
     * 根据任务ID删除文件同步日志
     * 
     * @param taskId 文件同步任务ID
     * @return 结果
     */
    public int deleteSyncFileSyncLogByTaskId(Long taskId);
} 