-- 数据同步-同步日志表
drop table if exists sync_log;
create table sync_log (
  log_id           bigint(20)      not null auto_increment    comment '日志ID',
  sync_id          bigint(20)      not null                   comment '同步任务ID',
  exec_time        datetime                                   comment '执行时间',
  success          char(1)         default '0'                comment '是否成功（0成功 1失败）',
  message          varchar(500)    default null               comment '日志消息',
  primary key (log_id)
) engine=innodb auto_increment=100 comment = '同步日志表';

-- 菜单 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('同步日志管理', (select menu_id from sys_menu where menu_name = '数据同步'), 4, 'synclog', 'datasync/synclog/index', 1, 0, 'C', '0', '0', 'datasync:synclog:list', 'form', 'admin', sysdate(), '', null, '同步日志菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('同步日志查询', @parentId, 1, '#', '', 1, 0, 'F', '0', '0', 'datasync:synclog:query', '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('同步日志删除', @parentId, 2, '#', '', 1, 0, 'F', '0', '0', 'datasync:synclog:remove', '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('同步日志导出', @parentId, 3, '#', '', 1, 0, 'F', '0', '0', 'datasync:synclog:export', '#', 'admin', sysdate(), '', null, ''); 