package com.ruoyi.web.controller.datasync;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.datasync.SyncDbSync;
import com.ruoyi.system.service.ISyncDbSyncService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.system.domain.datasync.SyncLog;
import com.ruoyi.system.service.ISyncLogService;

/**
 * 数据库同步Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/datasync/dbsync")
public class SyncDbSyncController extends BaseController
{
    @Autowired
    private ISyncDbSyncService syncDbSyncService;
    
    @Autowired
    private ISyncLogService syncLogService;

    /**
     * 查询数据库同步列表
     */
    @PreAuthorize("@ss.hasPermi('datasync:dbsync:list')")
    @GetMapping("/list")
    public TableDataInfo list(SyncDbSync syncDbSync)
    {
        startPage();
        List<SyncDbSync> list = syncDbSyncService.selectSyncDbSyncList(syncDbSync);
        return getDataTable(list);
    }

    /**
     * 导出数据库同步列表
     */
    @PreAuthorize("@ss.hasPermi('datasync:dbsync:export')")
    @Log(title = "数据库同步", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SyncDbSync syncDbSync)
    {
        List<SyncDbSync> list = syncDbSyncService.selectSyncDbSyncList(syncDbSync);
        ExcelUtil<SyncDbSync> util = new ExcelUtil<SyncDbSync>(SyncDbSync.class);
        util.exportExcel(response, list, "数据库同步数据");
    }

    /**
     * 获取数据库同步详细信息
     */
    @PreAuthorize("@ss.hasPermi('datasync:dbsync:query')")
    @GetMapping(value = "/{syncId}")
    public AjaxResult getInfo(@PathVariable("syncId") Long syncId)
    {
        return success(syncDbSyncService.selectSyncDbSyncBySyncId(syncId));
    }

    /**
     * 新增数据库同步
     */
    @PreAuthorize("@ss.hasPermi('datasync:dbsync:add')")
    @Log(title = "数据库同步", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SyncDbSync syncDbSync)
    {
        return toAjax(syncDbSyncService.insertSyncDbSync(syncDbSync));
    }

    /**
     * 修改数据库同步
     */
    @PreAuthorize("@ss.hasPermi('datasync:dbsync:edit')")
    @Log(title = "数据库同步", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SyncDbSync syncDbSync)
    {
        return toAjax(syncDbSyncService.updateSyncDbSync(syncDbSync));
    }

    /**
     * 删除数据库同步
     */
    @PreAuthorize("@ss.hasPermi('datasync:dbsync:remove')")
    @Log(title = "数据库同步", businessType = BusinessType.DELETE)
    @DeleteMapping("/{syncIds}")
    public AjaxResult remove(@PathVariable Long[] syncIds)
    {
        return toAjax(syncDbSyncService.deleteSyncDbSyncBySyncIds(syncIds));
    }
    
    /**
     * 执行数据库同步
     */
    @PreAuthorize("@ss.hasPermi('datasync:dbsync:execute')")
    @Log(title = "数据库同步", businessType = BusinessType.OTHER)
    @GetMapping("/execute/{syncId}")
    public AjaxResult execute(@PathVariable("syncId") Long syncId)
    {
        boolean result = syncDbSyncService.executeSyncTask(syncId);
        if (result) {
            return success("同步任务执行成功");
        } else {
            return error("同步任务执行失败");
        }
    }

    /**
     * 获取同步日志列表
     */
    @PreAuthorize("@ss.hasPermi('datasync:dbsync:query')")
    @GetMapping("/logs/{syncId}")
    public TableDataInfo logs(@PathVariable("syncId") Long syncId)
    {
        startPage();
        List<SyncLog> list = syncLogService.selectSyncLogBySyncId(syncId);
        return getDataTable(list);
    }
    
    /**
     * 获取同步日志详情
     */
    @PreAuthorize("@ss.hasPermi('datasync:dbsync:query')")
    @GetMapping("/log/detail/{logId}")
    public AjaxResult logDetail(@PathVariable("logId") Long logId)
    {
        return success(syncLogService.selectSyncLogByLogId(logId));
    }
} 