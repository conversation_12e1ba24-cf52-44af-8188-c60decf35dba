package com.ruoyi.system.service.impl;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.HashSet;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.Arrays;
import java.sql.Statement;
import java.sql.ResultSet;
import java.sql.PreparedStatement;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.DayOfWeek;
import java.time.temporal.TemporalAdjusters;
import java.time.format.DateTimeFormatter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.SyncDbSyncMapper;
import com.ruoyi.system.mapper.SyncDatabaseMapper;
import com.ruoyi.system.domain.datasync.SyncDbSync;
import com.ruoyi.system.domain.datasync.SyncDatabase;
import com.ruoyi.system.service.ISyncDbSyncService;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.datasync.SyncServer;
import com.ruoyi.system.domain.datasync.SyncLog;
import com.ruoyi.system.mapper.SyncServerMapper;
import com.ruoyi.system.mapper.SyncLogMapper;
import java.sql.DriverManager;

/**
 * 数据库同步Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class SyncDbSyncServiceImpl implements ISyncDbSyncService 
{
    @Autowired
    private SyncDbSyncMapper syncDbSyncMapper;
    
    @Autowired
    private SyncDatabaseMapper syncDatabaseMapper;

    @Autowired
    private SyncServerMapper syncServerMapper;

    @Autowired
    private SyncLogMapper syncLogMapper;

    /**
     * 查询数据库同步
     * 
     * @param syncId 数据库同步主键
     * @return 数据库同步
     */
    @Override
    public SyncDbSync selectSyncDbSyncBySyncId(Long syncId)
    {
        return syncDbSyncMapper.selectSyncDbSyncBySyncId(syncId);
    }

    /**
     * 查询数据库同步列表
     * 
     * @param syncDbSync 数据库同步
     * @return 数据库同步
     */
    @Override
    public List<SyncDbSync> selectSyncDbSyncList(SyncDbSync syncDbSync)
    {
        return syncDbSyncMapper.selectSyncDbSyncList(syncDbSync);
    }

    /**
     * 新增数据库同步
     * 
     * @param syncDbSync 数据库同步
     * @return 结果
     */
    @Override
    public int insertSyncDbSync(SyncDbSync syncDbSync)
    {
        // 设置默认值
        if (syncDbSync.getStatus() == null) {
            syncDbSync.setStatus("0");
        }
        
        // 获取数据库名称和服务器名称
        if (syncDbSync.getSourceDbId() != null) {
            SyncDatabase sourceDb = syncDatabaseMapper.selectSyncDatabaseByDbId(syncDbSync.getSourceDbId());
            if (sourceDb != null) {
                syncDbSync.setSourceDbName(sourceDb.getDbName());
                
                // 获取服务器信息
                SyncServer sourceServer = syncServerMapper.selectSyncServerByServerId(sourceDb.getServerId());
                if (sourceServer != null) {
                    // 设置源数据库服务器名称
                    syncDbSync.setSourceServerName(sourceServer.getServerName());
                }
            }
        }
        
        if (syncDbSync.getTargetDbId() != null) {
            SyncDatabase targetDb = syncDatabaseMapper.selectSyncDatabaseByDbId(syncDbSync.getTargetDbId());
            if (targetDb != null) {
                syncDbSync.setTargetDbName(targetDb.getDbName());
                
                // 获取服务器信息
                SyncServer targetServer = syncServerMapper.selectSyncServerByServerId(targetDb.getServerId());
                if (targetServer != null) {
                    // 设置目标数据库服务器名称
                    syncDbSync.setTargetServerName(targetServer.getServerName());
                }
            }
        }
        
        syncDbSync.setCreateTime(DateUtils.getNowDate());
        return syncDbSyncMapper.insertSyncDbSync(syncDbSync);
    }

    /**
     * 修改数据库同步
     * 
     * @param syncDbSync 数据库同步
     * @return 结果
     */
    @Override
    public int updateSyncDbSync(SyncDbSync syncDbSync)
    {
        // 获取数据库名称和服务器名称
        if (syncDbSync.getSourceDbId() != null) {
            SyncDatabase sourceDb = syncDatabaseMapper.selectSyncDatabaseByDbId(syncDbSync.getSourceDbId());
            if (sourceDb != null) {
                syncDbSync.setSourceDbName(sourceDb.getDbName());
                
                // 获取服务器信息
                SyncServer sourceServer = syncServerMapper.selectSyncServerByServerId(sourceDb.getServerId());
                if (sourceServer != null) {
                    // 设置源数据库服务器名称
                    syncDbSync.setSourceServerName(sourceServer.getServerName());
                }
            }
        }
        
        if (syncDbSync.getTargetDbId() != null) {
            SyncDatabase targetDb = syncDatabaseMapper.selectSyncDatabaseByDbId(syncDbSync.getTargetDbId());
            if (targetDb != null) {
                syncDbSync.setTargetDbName(targetDb.getDbName());
                
                // 获取服务器信息
                SyncServer targetServer = syncServerMapper.selectSyncServerByServerId(targetDb.getServerId());
                if (targetServer != null) {
                    // 设置目标数据库服务器名称
                    syncDbSync.setTargetServerName(targetServer.getServerName());
                }
            }
        }
        
        syncDbSync.setUpdateTime(DateUtils.getNowDate());
        return syncDbSyncMapper.updateSyncDbSync(syncDbSync);
    }

    /**
     * 批量删除数据库同步
     * 
     * @param syncIds 需要删除的数据库同步主键
     * @return 结果
     */
    @Override
    public int deleteSyncDbSyncBySyncIds(Long[] syncIds)
    {
        return syncDbSyncMapper.deleteSyncDbSyncBySyncIds(syncIds);
    }

    /**
     * 删除数据库同步信息
     * 
     * @param syncId 数据库同步主键
     * @return 结果
     */
    @Override
    public int deleteSyncDbSyncBySyncId(Long syncId)
    {
        return syncDbSyncMapper.deleteSyncDbSyncBySyncId(syncId);
    }
    
    /**
     * 执行数据库同步
     * 
     * @param syncId 数据库同步主键
     * @return 结果
     */
    @Override
    public boolean executeSyncTask(Long syncId)
    {
        // 获取同步任务信息
        SyncDbSync syncTask = syncDbSyncMapper.selectSyncDbSyncBySyncId(syncId);
        if (syncTask == null || !"0".equals(syncTask.getStatus())) {
            System.out.println("同步任务不存在或状态不正常，syncId=" + syncId + ", status=" + (syncTask != null ? syncTask.getStatus() : "null"));
            return false;
        }
        
        // 获取源数据库和目标数据库
        SyncDatabase sourceDb = syncDatabaseMapper.selectSyncDatabaseByDbId(syncTask.getSourceDbId());
        SyncDatabase targetDb = syncDatabaseMapper.selectSyncDatabaseByDbId(syncTask.getTargetDbId());
        if (sourceDb == null || targetDb == null) {
            System.out.println("源数据库或目标数据库不存在，sourceDbId=" + syncTask.getSourceDbId() + ", targetDbId=" + syncTask.getTargetDbId());
            return false;
        }
        
        System.out.println("源数据库: " + sourceDb.getDbName() + ", 类型: " + sourceDb.getDbType());
        System.out.println("目标数据库: " + targetDb.getDbName() + ", 类型: " + targetDb.getDbType());
        
        // 获取源服务器和目标服务器
        SyncServer sourceServer = syncServerMapper.selectSyncServerByServerId(sourceDb.getServerId());
        SyncServer targetServer = syncServerMapper.selectSyncServerByServerId(targetDb.getServerId());
        if (sourceServer == null || targetServer == null) {
            System.out.println("源服务器或目标服务器不存在，sourceServerId=" + sourceDb.getServerId() + ", targetServerId=" + targetDb.getServerId());
            return false;
        }
        
        System.out.println("源服务器: " + sourceServer.getServerName() + ", IP: " + sourceServer.getIpAddress());
        System.out.println("目标服务器: " + targetServer.getServerName() + ", IP: " + targetServer.getIpAddress());
        
        Connection sourceConn = null;
        Connection targetConn = null;
        
        try {
            // 建立源数据库和目标数据库的连接
            System.out.println("正在连接源数据库...");
            sourceConn = getConnection(sourceDb, sourceServer);
            System.out.println("正在连接目标数据库...");
            targetConn = getConnection(targetDb, targetServer);
            
            if (sourceConn == null || targetConn == null) {
                String errorMsg = "无法连接到数据库，sourceConn=" + (sourceConn != null ? "成功" : "失败") 
                    + ", targetConn=" + (targetConn != null ? "成功" : "失败");
                System.out.println(errorMsg);
                throw new Exception(errorMsg);
            }
            
            // 获取要同步的表列表
            List<String> tableList;
            if (StringUtils.isEmpty(syncTask.getSyncTables())) {
                // 如果没有指定表，则同步所有表
                tableList = getAllTables(sourceConn, sourceDb.getDbName());
            } else {
                // 如果指定了表，则同步指定的表
                tableList = Arrays.asList(syncTask.getSyncTables().split(","));
            }
            
            if (tableList.isEmpty()) {
                throw new Exception("没有可同步的表");
            }
            
            // 根据同步类型执行全量或增量同步
            if ("1".equals(syncTask.getSyncMode())) {
                // 全量同步
                for (String tableName : tableList) {
                    syncTable(sourceConn, targetConn, tableName, sourceDb, targetDb, false);
                }
            } else if ("2".equals(syncTask.getSyncMode())) {
                // 增量同步
                for (String tableName : tableList) {
                    syncTable(sourceConn, targetConn, tableName, sourceDb, targetDb, true);
                }
            }
            
            // 更新最后执行时间和下次执行时间
            syncTask.setLastExecTime(DateUtils.getTime());
            
            // 计算下次执行时间
            if (!"0".equals(syncTask.getExecCycle()) && StringUtils.isNotEmpty(syncTask.getExecTime())) {
                syncTask.setNextExecTime(calculateNextExecTime(syncTask.getExecCycle(), syncTask.getExecTime()));
            }
            
            syncDbSyncMapper.updateSyncDbSync(syncTask);
            
            // 记录同步日志
            saveSyncLog(syncId, true, "同步成功，同步了 " + tableList.size() + " 个表");
            
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            // 记录同步日志
            saveSyncLog(syncId, false, "同步失败：" + e.getMessage());
            return false;
        } finally {
            closeConnection(sourceConn, null, null);
            closeConnection(targetConn, null, null);
        }
    }

    /**
     * 获取数据库所有表
     */
    private List<String> getAllTables(Connection conn, String dbName) throws SQLException {
        List<String> tables = new ArrayList<>();
        DatabaseMetaData metaData = conn.getMetaData();
        ResultSet rs = metaData.getTables(dbName, null, "%", new String[]{"TABLE"});
        
        while (rs.next()) {
            tables.add(rs.getString("TABLE_NAME"));
        }
        
        rs.close();
        return tables;
    }

    /**
     * 同步表
     */
    private void syncTable(Connection sourceConn, Connection targetConn, String tableName, 
                          SyncDatabase sourceDb, SyncDatabase targetDb, boolean isIncremental) throws Exception {
        // 1. 获取表结构
        Map<String, ColumnInfo> columns = getTableColumns(sourceConn, sourceDb.getDbName(), tableName);
        if (columns.isEmpty()) {
            throw new Exception("表 " + tableName + " 结构获取失败");
        }
        
        // 2. 检查目标表是否存在，不存在则创建
        boolean tableExists = checkTableExists(targetConn, targetDb.getDbName(), tableName);
        if (!tableExists) {
            createTable(targetConn, targetDb.getDbName(), tableName, columns);
        } else {
            // 表存在，检查并更新表结构
            updateTableStructure(targetConn, targetDb.getDbName(), tableName, columns);
        }
        
        // 3. 同步数据
        if (isIncremental) {
            // 增量同步
            syncDataIncremental(sourceConn, targetConn, tableName, columns);
        } else {
            // 全量同步
            syncDataFull(sourceConn, targetConn, tableName, columns);
        }
    }

    /**
     * 获取表字段信息
     */
    private Map<String, ColumnInfo> getTableColumns(Connection conn, String dbName, String tableName) throws SQLException {
        Map<String, ColumnInfo> columns = new LinkedHashMap<>();
        DatabaseMetaData metaData = conn.getMetaData();
        
        // 获取主键信息
        Set<String> primaryKeys = new HashSet<>();
        ResultSet pkRs = metaData.getPrimaryKeys(dbName, null, tableName);
        while (pkRs.next()) {
            primaryKeys.add(pkRs.getString("COLUMN_NAME"));
        }
        pkRs.close();
        
        // 获取字段信息
        ResultSet rs = metaData.getColumns(dbName, null, tableName, "%");
        while (rs.next()) {
            String columnName = rs.getString("COLUMN_NAME");
            ColumnInfo column = new ColumnInfo();
            column.setName(columnName);
            
            // 获取并可能修正列类型
            String typeName = rs.getString("TYPE_NAME");
            // 对于DATETIME和TIMESTAMP类型，不保留精度信息
            if (typeName.toUpperCase().contains("DATETIME") || typeName.toUpperCase().contains("TIMESTAMP")) {
                // 去除可能的精度部分，如DATETIME(6)只保留DATETIME
                typeName = typeName.replaceAll("\\(\\d+\\)", "");
            }
            column.setType(typeName);
            
            column.setSize(rs.getInt("COLUMN_SIZE"));
            column.setDecimalDigits(rs.getInt("DECIMAL_DIGITS"));
            column.setNullable(rs.getInt("NULLABLE") == DatabaseMetaData.columnNullable);
            column.setDefaultValue(rs.getString("COLUMN_DEF"));
            column.setPrimaryKey(primaryKeys.contains(columnName));
            column.setAutoIncrement(rs.getString("IS_AUTOINCREMENT").equals("YES"));
            
            columns.put(columnName, column);
        }
        rs.close();
        
        return columns;
    }

    /**
     * 检查表是否存在
     */
    private boolean checkTableExists(Connection conn, String dbName, String tableName) throws SQLException {
        DatabaseMetaData metaData = conn.getMetaData();
        ResultSet rs = metaData.getTables(dbName, null, tableName, new String[]{"TABLE"});
        boolean exists = rs.next();
        rs.close();
        return exists;
    }

    /**
     * 创建表
     */
    private void createTable(Connection conn, String dbName, String tableName, Map<String, ColumnInfo> columns) throws SQLException {
        // 临时禁用MySQL的严格模式，允许使用零日期值
        Statement modeStmt = null;
        try {
            modeStmt = conn.createStatement();
            modeStmt.execute("SET SESSION sql_mode = ''");
        } catch (Exception e) {
            // 忽略设置SQL_MODE的错误
            System.out.println("设置SQL_MODE失败: " + e.getMessage());
        } finally {
            if (modeStmt != null) {
                modeStmt.close();
            }
        }

        StringBuilder sql = new StringBuilder();
        sql.append("CREATE TABLE ").append(tableName).append(" (");
        
        List<String> primaryKeys = new ArrayList<>();
        
        int i = 0;
        for (Map.Entry<String, ColumnInfo> entry : columns.entrySet()) {
            ColumnInfo column = entry.getValue();
            String columnType = column.getType();

            // 检查列类型是否为空
            if (columnType == null || columnType.trim().isEmpty()) {
                System.out.println("警告：列 " + column.getName() + " 的类型为空，跳过该列");
                continue;
            }

            if (i > 0) {
                sql.append(", ");
            }

            // 对列名进行转义处理，防止使用MySQL保留关键字
            String columnName = escapeColumnName(column.getName());
            sql.append(columnName).append(" ");

            // 处理列类型，确保DATETIME和TIMESTAMP类型不带精度
            if (columnType.toUpperCase().contains("DATETIME") || columnType.toUpperCase().contains("TIMESTAMP")) {
                // 移除可能的精度部分，如DATETIME(6)只保留DATETIME
                columnType = columnType.replaceAll("\\(\\d+\\)", "");
            }

            // 处理INT UNSIGNED类型的特殊情况
            String processedColumnType = processColumnType(columnType, column.getSize(), column.getDecimalDigits());

            // 验证处理后的类型不为空
            if (processedColumnType == null || processedColumnType.trim().isEmpty()) {
                System.err.println("错误：列 " + column.getName() + " 处理后的类型为空！原类型=" + columnType);
                processedColumnType = "VARCHAR(255)"; // 使用默认类型
            }

            sql.append(processedColumnType);

            // 调试信息：输出当前列的处理结果
            System.err.println("列 " + column.getName() + ": 原类型=" + columnType + ", 处理后=" + processedColumnType);
            
            if (!column.isNullable()) {
                sql.append(" NOT NULL");
            }
            
            // 特殊处理created_at字段和时间戳字段
            String lowerColumnName = column.getName().toLowerCase();
            columnType = columnType.toUpperCase();
            
            if (lowerColumnName.equals("created_at") || lowerColumnName.equals("updated_at") ||
                lowerColumnName.equals("create_time") || lowerColumnName.equals("update_time")) {
                
                if (columnType.contains("TIMESTAMP") || columnType.contains("DATETIME")) {
                    // 对于timestamp和datetime类型使用固定且有效的默认值
                    if (column.isNullable()) {
                        sql.append(" DEFAULT NULL");
                    } else {
                        sql.append(" DEFAULT CURRENT_TIMESTAMP");
                    }
                }
            } else if (column.getDefaultValue() != null) {
                String defaultValue = column.getDefaultValue();
                
                // 特殊处理日期时间类型的默认值
                if ((columnType.contains("TIMESTAMP") || columnType.contains("DATETIME") || columnType.contains("DATE"))) {
                    if (defaultValue.toUpperCase().contains("CURRENT_TIMESTAMP") || defaultValue.toUpperCase().contains("NOW()")) {
                        sql.append(" DEFAULT CURRENT_TIMESTAMP");
                    } else if (defaultValue.equals("0000-00-00 00:00:00") || defaultValue.equals("0000-00-00")) {
                        if (column.isNullable()) {
                            sql.append(" DEFAULT NULL");
                        } else {
                            sql.append(" DEFAULT CURRENT_TIMESTAMP");
                        }
                    } else if (defaultValue.startsWith("'") && defaultValue.endsWith("'")) {
                        // 已带引号的日期值
                        sql.append(" DEFAULT ").append(defaultValue);
                    } else {
                        // 针对其他日期值，默认加上引号
                        sql.append(" DEFAULT '").append(defaultValue).append("'");
                    }
                } else {
                    // 非日期类型的处理
                    if (columnType.contains("CHAR") || columnType.contains("TEXT")) {
                        if (defaultValue.startsWith("'") && defaultValue.endsWith("'")) {
                            sql.append(" DEFAULT ").append(defaultValue);
                        } else {
                            sql.append(" DEFAULT '").append(defaultValue).append("'");
                        }
                    } else {
                        sql.append(" DEFAULT ").append(defaultValue);
                    }
                }
            }
            
            if (column.isAutoIncrement()) {
                sql.append(" AUTO_INCREMENT");
            }
            
            if (column.isPrimaryKey()) {
                primaryKeys.add(column.getName());
            }

            // 调试信息：输出当前SQL状态
            System.out.println("处理完列 " + column.getName() + " 后的SQL片段: " + sql.toString().substring(Math.max(0, sql.length() - 100)));

            i++;
        }
        
        // 添加主键
        if (!primaryKeys.isEmpty()) {
            sql.append(", PRIMARY KEY (");
            for (int j = 0; j < primaryKeys.size(); j++) {
                if (j > 0) {
                    sql.append(", ");
                }
                // 对主键列名也进行转义处理
                sql.append(escapeColumnName(primaryKeys.get(j)));
            }
            sql.append(")");
        }
        
        sql.append(") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");
        
        Statement stmt = null;
        try {
            stmt = conn.createStatement();
            String finalSql = sql.toString();
            System.out.println("创建表SQL: " + finalSql);
            System.out.println("SQL长度: " + finalSql.length());

            // 检查SQL中是否有异常字符
            if (finalSql.contains("  ")) {
                System.out.println("警告：SQL中包含多个连续空格");
            }
            if (finalSql.contains(" ,")) {
                System.out.println("警告：SQL中包含空格+逗号的组合");
            }

            stmt.execute(finalSql);
        } finally {
            if (stmt != null) {
                stmt.close();
            }
        }
    }

    /**
     * 处理列类型，确保生成正确的SQL语法
     */
    private String processColumnType(String columnType, int size, int decimalDigits) {
        if (columnType == null || columnType.trim().isEmpty()) {
            System.out.println("警告：列类型为空，返回默认类型 VARCHAR(255)");
            return "VARCHAR(255)";
        }

        String upperType = columnType.toUpperCase();

        // 处理INT UNSIGNED类型
        if (upperType.contains("INT") && upperType.contains("UNSIGNED")) {
            // 提取基础类型（INT, BIGINT, SMALLINT等）
            String baseType = upperType.replaceAll("\\s*UNSIGNED.*", "").trim();

            // 对于INT类型，在MySQL 8.0+中不建议使用显示宽度，直接返回 INT UNSIGNED
            if (baseType.equals("INT")) {
                return "INT UNSIGNED";
            } else if (baseType.equals("BIGINT")) {
                return "BIGINT UNSIGNED";
            } else if (baseType.equals("SMALLINT")) {
                return "SMALLINT UNSIGNED";
            } else if (baseType.equals("TINYINT")) {
                return "TINYINT UNSIGNED";
            } else if (baseType.equals("MEDIUMINT")) {
                return "MEDIUMINT UNSIGNED";
            }
        }

        // 处理其他数值类型
        if (upperType.contains("INT") && !upperType.contains("UNSIGNED")) {
            String baseType = upperType.replaceAll("\\(\\d+\\)", "").trim();
            if (baseType.equals("INT")) {
                return "INT";
            } else if (baseType.equals("BIGINT")) {
                return "BIGINT";
            } else if (baseType.equals("SMALLINT")) {
                return "SMALLINT";
            } else if (baseType.equals("TINYINT")) {
                return "TINYINT";
            } else if (baseType.equals("MEDIUMINT")) {
                return "MEDIUMINT";
            }
        }

        // 处理日期时间类型，不需要精度
        if (upperType.contains("DATETIME") || upperType.contains("TIMESTAMP") ||
            upperType.contains("DATE") || upperType.contains("TIME")) {
            return columnType.replaceAll("\\(\\d+\\)", "");
        }

        // 处理需要精度的类型（VARCHAR, CHAR, DECIMAL等）
        if (upperType.contains("VARCHAR") || upperType.contains("CHAR") ||
            upperType.contains("DECIMAL") || upperType.contains("NUMERIC") ||
            upperType.contains("FLOAT") || upperType.contains("DOUBLE")) {

            String baseType = columnType.replaceAll("\\(.*\\)", "");

            if (size > 0) {
                if (decimalDigits > 0 && (upperType.contains("DECIMAL") || upperType.contains("NUMERIC"))) {
                    return baseType + "(" + size + "," + decimalDigits + ")";
                } else {
                    return baseType + "(" + size + ")";
                }
            } else {
                // 如果没有size信息，但是是需要精度的类型，保留原始定义
                return columnType;
            }
        }

        // 对于其他类型，直接返回原类型（去除可能的精度信息）
        String result = columnType.replaceAll("\\(\\d+\\)", "");

        // 确保结果不为空
        if (result == null || result.trim().isEmpty()) {
            System.out.println("警告：处理后的列类型为空，原类型=" + columnType + "，返回原类型");
            return columnType;
        }

        return result;
    }

    /**
     * 转义列名，处理MySQL保留关键字
     */
    private String escapeColumnName(String columnName) {
        // MySQL保留关键字列表（常见的）
        String[] reservedWords = {
            "ADD", "ALL", "ALTER", "ANALYZE", "AND", "AS", "ASC", "ASENSITIVE",
            "BEFORE", "BETWEEN", "BIGINT", "BINARY", "BLOB", "BOTH", "BY",
            "CALL", "CASCADE", "CASE", "CHANGE", "CHAR", "CHARACTER", "CHECK",
            "COLLATE", "COLUMN", "CONDITION", "CONSTRAINT", "CONTINUE", "CONVERT",
            "CREATE", "CROSS", "CURRENT_DATE", "CURRENT_TIME", "CURRENT_TIMESTAMP",
            "CURRENT_USER", "CURSOR", "DATABASE", "DATABASES", "DAY_HOUR",
            "DAY_MICROSECOND", "DAY_MINUTE", "DAY_SECOND", "DEC", "DECIMAL",
            "DECLARE", "DEFAULT", "DELAYED", "DELETE", "DESC", "DESCRIBE",
            "DETERMINISTIC", "DISTINCT", "DISTINCTROW", "DIV", "DOUBLE", "DROP",
            "DUAL", "EACH", "ELSE", "ELSEIF", "ENCLOSED", "ESCAPED", "EXISTS",
            "EXIT", "EXPLAIN", "FALSE", "FETCH", "FLOAT", "FLOAT4", "FLOAT8",
            "FOR", "FORCE", "FOREIGN", "FROM", "FULLTEXT", "GRANT", "GROUP",
            "HAVING", "HIGH_PRIORITY", "HOUR_MICROSECOND", "HOUR_MINUTE",
            "HOUR_SECOND", "IF", "IGNORE", "IN", "INDEX", "INFILE", "INNER",
            "INOUT", "INSENSITIVE", "INSERT", "INT", "INT1", "INT2", "INT3",
            "INT4", "INT8", "INTEGER", "INTERVAL", "INTO", "IS", "ITERATE",
            "JOIN", "KEY", "KEYS", "KILL", "LEADING", "LEAVE", "LEFT", "LIKE",
            "LIMIT", "LINEAR", "LINES", "LOAD", "LOCALTIME", "LOCALTIMESTAMP",
            "LOCK", "LONG", "LONGBLOB", "LONGTEXT", "LOOP", "LOW_PRIORITY",
            "MATCH", "MEDIUMBLOB", "MEDIUMINT", "MEDIUMTEXT", "MIDDLEINT",
            "MINUTE_MICROSECOND", "MINUTE_SECOND", "MOD", "MODIFIES", "NATURAL",
            "NOT", "NO_WRITE_TO_BINLOG", "NULL", "NUMERIC", "ON", "OPTIMIZE",
            "OPTION", "OPTIONALLY", "OR", "ORDER", "OUT", "OUTER", "OUTFILE",
            "PRECISION", "PRIMARY", "PROCEDURE", "PURGE", "RAID0", "RANGE",
            "READ", "READS", "REAL", "REFERENCES", "REGEXP", "RELEASE", "RENAME",
            "REPEAT", "REPLACE", "REQUIRE", "RESTRICT", "RETURN", "REVOKE",
            "RIGHT", "RLIKE", "SCHEMA", "SCHEMAS", "SECOND_MICROSECOND", "SELECT",
            "SENSITIVE", "SEPARATOR", "SET", "SHOW", "SMALLINT", "SPATIAL",
            "SPECIFIC", "SQL", "SQLEXCEPTION", "SQLSTATE", "SQLWARNING",
            "SQL_BIG_RESULT", "SQL_CALC_FOUND_ROWS", "SQL_SMALL_RESULT", "SSL",
            "STARTING", "STRAIGHT_JOIN", "TABLE", "TERMINATED", "THEN", "TINYBLOB",
            "TINYINT", "TINYTEXT", "TO", "TRAILING", "TRIGGER", "TRUE", "UNDO",
            "UNION", "UNIQUE", "UNLOCK", "UNSIGNED", "UPDATE", "USAGE", "USE",
            "USING", "UTC_DATE", "UTC_TIME", "UTC_TIMESTAMP", "VALUES", "VARBINARY",
            "VARCHAR", "VARCHARACTER", "VARYING", "WHEN", "WHERE", "WHILE",
            "WITH", "WRITE", "X509", "XOR", "YEAR_MONTH", "ZEROFILL"
        };

        // 检查是否为保留关键字（不区分大小写）
        for (String reserved : reservedWords) {
            if (reserved.equalsIgnoreCase(columnName)) {
                return "`" + columnName + "`";
            }
        }

        // 如果不是保留关键字，直接返回原列名
        return columnName;
    }

    /**
     * 更新表结构
     */
    private void updateTableStructure(Connection conn, String dbName, String tableName, Map<String, ColumnInfo> sourceColumns) throws SQLException {
        // 获取目标表的列
        Map<String, ColumnInfo> targetColumns = getTableColumns(conn, dbName, tableName);
        
        // 查找需要添加的列
        List<ColumnInfo> columnsToAdd = new ArrayList<>();
        for (Map.Entry<String, ColumnInfo> entry : sourceColumns.entrySet()) {
            String columnName = entry.getKey();
            ColumnInfo sourceColumn = entry.getValue();
            
            if (!targetColumns.containsKey(columnName)) {
                columnsToAdd.add(sourceColumn);
            }
        }
        
        // 添加新列
        if (!columnsToAdd.isEmpty()) {
            for (ColumnInfo column : columnsToAdd) {
                StringBuilder sql = new StringBuilder();
                sql.append("ALTER TABLE ").append(tableName);
                sql.append(" ADD COLUMN ").append(column.getName()).append(" ");
                sql.append(column.getType());
                
                if (column.getSize() > 0) {
                    if (column.getDecimalDigits() > 0) {
                        sql.append("(").append(column.getSize()).append(",").append(column.getDecimalDigits()).append(")");
                    } else {
                        sql.append("(").append(column.getSize()).append(")");
                    }
                }
                
                if (!column.isNullable()) {
                    sql.append(" NOT NULL");
                }
                
                if (column.getDefaultValue() != null) {
                    String defaultValue = column.getDefaultValue();
                    
                    // 特殊处理日期时间类型的默认值
                    String columnType = column.getType().toUpperCase();
                    if ((columnType.contains("TIMESTAMP") || columnType.contains("DATETIME") || columnType.contains("DATE")) 
                            && (defaultValue.toUpperCase().contains("CURRENT_TIMESTAMP") 
                                || defaultValue.toUpperCase().contains("NOW()")
                                || defaultValue.equals("0000-00-00 00:00:00")
                                || defaultValue.equals("0000-00-00"))) {
                        
                        // 对于CURRENT_TIMESTAMP，直接使用不带引号
                        if (defaultValue.toUpperCase().contains("CURRENT_TIMESTAMP") || defaultValue.toUpperCase().contains("NOW()")) {
                            sql.append(" DEFAULT CURRENT_TIMESTAMP");
                        } 
                        // 对于0000-00-00 00:00:00这种值，MySQL严格模式下不允许，改用NULL
                        else if (defaultValue.equals("0000-00-00 00:00:00") || defaultValue.equals("0000-00-00")) {
                            if (column.isNullable()) {
                                sql.append(" DEFAULT NULL");
                            } else {
                                // 如果不允许为NULL，则使用一个有效的日期
                                sql.append(" DEFAULT '2000-01-01 00:00:00'");
                            }
                        }
                    } else {
                        // 如果是字符串类型需要加引号，其他类型直接使用
                        if (columnType.contains("CHAR") || columnType.contains("TEXT") || columnType.contains("DATE") 
                                || columnType.contains("TIME") || columnType.contains("YEAR")) {
                            // 已经有引号的不要重复添加
                            if (defaultValue.startsWith("'") && defaultValue.endsWith("'")) {
                                sql.append(" DEFAULT ").append(defaultValue);
                            } else {
                                sql.append(" DEFAULT '").append(defaultValue).append("'");
                            }
                        } else {
                            sql.append(" DEFAULT ").append(defaultValue);
                        }
                    }
                }
                
                Statement stmt = conn.createStatement();
                stmt.execute(sql.toString());
                stmt.close();
            }
        }
    }

    /**
     * 全量同步数据
     */
    private void syncDataFull(Connection sourceConn, Connection targetConn, String tableName, Map<String, ColumnInfo> columns) throws SQLException {
        // 1. 清空目标表数据
        Statement truncateStmt = targetConn.createStatement();
        truncateStmt.execute("TRUNCATE TABLE " + tableName);
        truncateStmt.close();
        
        // 2. 获取源表数据
        Statement sourceStmt = sourceConn.createStatement();
        ResultSet rs = sourceStmt.executeQuery("SELECT * FROM " + tableName);
        
        // 3. 插入数据到目标表
        insertDataBatch(targetConn, tableName, columns, rs);
        
        sourceStmt.close();
        rs.close();
    }

    /**
     * 增量同步数据
     */
    private void syncDataIncremental(Connection sourceConn, Connection targetConn, String tableName, Map<String, ColumnInfo> columns) throws SQLException {
        // 获取主键列
        List<String> primaryKeys = getPrimaryKeys(columns);
        if (primaryKeys.isEmpty()) {
            // 如果没有主键，则进行全量同步
            syncDataFull(sourceConn, targetConn, tableName, columns);
            return;
        }
        
        // 1. 获取源表数据
        Statement sourceStmt = sourceConn.createStatement();
        ResultSet rs = sourceStmt.executeQuery("SELECT * FROM " + tableName);
        
        // 2. 根据主键更新或插入数据
        while (rs.next()) {
            StringBuilder whereClause = new StringBuilder();
            for (int i = 0; i < primaryKeys.size(); i++) {
                String pkColumn = primaryKeys.get(i);
                if (i > 0) {
                    whereClause.append(" AND ");
                }
                Object pkValue = rs.getObject(pkColumn);
                if (pkValue == null) {
                    whereClause.append(pkColumn).append(" IS NULL");
                } else {
                    whereClause.append(pkColumn).append(" = ?");
                }
            }
            
            // 检查记录是否存在
            StringBuilder checkSql = new StringBuilder();
            checkSql.append("SELECT COUNT(*) FROM ").append(tableName);
            checkSql.append(" WHERE ").append(whereClause);
            
            PreparedStatement checkStmt = targetConn.prepareStatement(checkSql.toString());
            int paramIndex = 1;
            for (String pkColumn : primaryKeys) {
                Object pkValue = rs.getObject(pkColumn);
                if (pkValue != null) {
                    checkStmt.setObject(paramIndex++, pkValue);
                }
            }
            
            ResultSet checkRs = checkStmt.executeQuery();
            checkRs.next();
            int count = checkRs.getInt(1);
            checkRs.close();
            checkStmt.close();
            
            if (count > 0) {
                // 记录存在，执行更新
                updateRecord(targetConn, tableName, columns, primaryKeys, rs);
            } else {
                // 记录不存在，执行插入
                insertRecord(targetConn, tableName, columns, rs);
            }
        }
        
        sourceStmt.close();
        rs.close();
    }

    /**
     * 获取主键列表
     */
    private List<String> getPrimaryKeys(Map<String, ColumnInfo> columns) {
        List<String> primaryKeys = new ArrayList<>();
        for (Map.Entry<String, ColumnInfo> entry : columns.entrySet()) {
            if (entry.getValue().isPrimaryKey()) {
                primaryKeys.add(entry.getKey());
            }
        }
        return primaryKeys;
    }

    /**
     * 批量插入数据
     */
    private void insertDataBatch(Connection conn, String tableName, Map<String, ColumnInfo> columns, ResultSet sourceRs) throws SQLException {
        // 准备插入语句
        StringBuilder sql = new StringBuilder();
        sql.append("INSERT INTO ").append(tableName).append(" (");
        
        int i = 0;
        for (String columnName : columns.keySet()) {
            if (i > 0) {
                sql.append(", ");
            }
            sql.append(columnName);
            i++;
        }
        
        sql.append(") VALUES (");
        
        for (i = 0; i < columns.size(); i++) {
            if (i > 0) {
                sql.append(", ");
            }
            sql.append("?");
        }
        
        sql.append(")");
        
        PreparedStatement pstmt = conn.prepareStatement(sql.toString());
        int batchSize = 0;
        int maxBatchSize = 1000; // 每批处理的最大记录数
        
        conn.setAutoCommit(false);
        
        while (sourceRs.next()) {
            int paramIndex = 1;
            for (String columnName : columns.keySet()) {
                Object value = sourceRs.getObject(columnName);
                pstmt.setObject(paramIndex++, value);
            }
            
            pstmt.addBatch();
            batchSize++;
            
            if (batchSize >= maxBatchSize) {
                pstmt.executeBatch();
                conn.commit();
                batchSize = 0;
            }
        }
        
        if (batchSize > 0) {
            pstmt.executeBatch();
            conn.commit();
        }
        
        conn.setAutoCommit(true);
        pstmt.close();
    }

    /**
     * 更新记录
     */
    private void updateRecord(Connection conn, String tableName, Map<String, ColumnInfo> columns, List<String> primaryKeys, ResultSet sourceRs) throws SQLException {
        // 准备更新语句
        StringBuilder sql = new StringBuilder();
        sql.append("UPDATE ").append(tableName).append(" SET ");
        
        List<String> updateColumns = new ArrayList<>();
        for (String columnName : columns.keySet()) {
            if (!primaryKeys.contains(columnName)) {
                updateColumns.add(columnName);
            }
        }
        
        for (int i = 0; i < updateColumns.size(); i++) {
            if (i > 0) {
                sql.append(", ");
            }
            sql.append(updateColumns.get(i)).append(" = ?");
        }
        
        sql.append(" WHERE ");
        
        for (int i = 0; i < primaryKeys.size(); i++) {
            if (i > 0) {
                sql.append(" AND ");
            }
            sql.append(primaryKeys.get(i)).append(" = ?");
        }
        
        PreparedStatement pstmt = conn.prepareStatement(sql.toString());
        
        // 设置更新字段的值
        int paramIndex = 1;
        for (String columnName : updateColumns) {
            Object value = sourceRs.getObject(columnName);
            pstmt.setObject(paramIndex++, value);
        }
        
        // 设置主键字段的值
        for (String pkColumn : primaryKeys) {
            Object pkValue = sourceRs.getObject(pkColumn);
            pstmt.setObject(paramIndex++, pkValue);
        }
        
        pstmt.executeUpdate();
        pstmt.close();
    }

    /**
     * 插入记录
     */
    private void insertRecord(Connection conn, String tableName, Map<String, ColumnInfo> columns, ResultSet sourceRs) throws SQLException {
        // 准备插入语句
        StringBuilder sql = new StringBuilder();
        sql.append("INSERT INTO ").append(tableName).append(" (");
        
        int i = 0;
        for (String columnName : columns.keySet()) {
            if (i > 0) {
                sql.append(", ");
            }
            sql.append(columnName);
            i++;
        }
        
        sql.append(") VALUES (");
        
        for (i = 0; i < columns.size(); i++) {
            if (i > 0) {
                sql.append(", ");
            }
            sql.append("?");
        }
        
        sql.append(")");
        
        PreparedStatement pstmt = conn.prepareStatement(sql.toString());
        
        int paramIndex = 1;
        for (String columnName : columns.keySet()) {
            Object value = sourceRs.getObject(columnName);
            pstmt.setObject(paramIndex++, value);
        }
        
        pstmt.executeUpdate();
        pstmt.close();
    }

    /**
     * 计算下次执行时间
     */
    private String calculateNextExecTime(String execCycle, String execTime) {
        LocalDateTime now = LocalDateTime.now();
        LocalTime time = LocalTime.parse(execTime);
        
        LocalDateTime nextExec = now.withHour(time.getHour()).withMinute(time.getMinute()).withSecond(0);
        
        switch (execCycle) {
            case "1": // 每天
                if (now.isAfter(nextExec)) {
                    nextExec = nextExec.plusDays(1);
                }
                break;
            case "2": // 每周
                nextExec = nextExec.with(TemporalAdjusters.next(DayOfWeek.MONDAY));
                break;
            case "3": // 每月
                nextExec = nextExec.with(TemporalAdjusters.firstDayOfNextMonth());
                break;
        }
        
        return DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(nextExec);
    }

    /**
     * 记录同步日志
     */
    private void saveSyncLog(Long syncId, boolean success, String message) {
        SyncLog log = new SyncLog();
        log.setSyncId(syncId);
        log.setExecTime(DateUtils.getNowDate());
        log.setSuccess(success ? "0" : "1"); // 0-成功，1-失败
        log.setMessage(message);
        
        syncLogMapper.insertSyncLog(log);
    }

    /**
     * 获取数据库连接
     */
    private Connection getConnection(SyncDatabase database, SyncServer server) {
        try {
            // 构建JDBC URL
            StringBuilder urlBuilder = new StringBuilder("jdbc:");
            String dbType = database.getDbType();
            String driverClass = null;
            
            // 根据数据库类型构建JDBC URL
            if ("1".equals(dbType) || "MySQL".equalsIgnoreCase(dbType)) { 
                urlBuilder.append("mysql://").append(server.getIpAddress())
                        .append(":").append(database.getDbPort())
                        .append("/").append(database.getDbName());
                urlBuilder.append("?useUnicode=true&characterEncoding=utf8&useSSL=false");
                driverClass = "com.mysql.cj.jdbc.Driver";
            } else if ("2".equals(dbType) || "Oracle".equalsIgnoreCase(dbType)) { 
                urlBuilder.append("oracle:thin:@").append(server.getIpAddress())
                        .append(":").append(database.getDbPort())
                        .append(":").append(database.getDbName());
                driverClass = "oracle.jdbc.driver.OracleDriver";
            } else if ("3".equals(dbType) || "SQLServer".equalsIgnoreCase(dbType)) { 
                urlBuilder.append("sqlserver://").append(server.getIpAddress())
                        .append(":").append(database.getDbPort())
                        .append(";databaseName=").append(database.getDbName());
                driverClass = "com.microsoft.sqlserver.jdbc.SQLServerDriver";
            } else if ("4".equals(dbType) || "PostgreSQL".equalsIgnoreCase(dbType)) { 
                urlBuilder.append("postgresql://").append(server.getIpAddress())
                        .append(":").append(database.getDbPort())
                        .append("/").append(database.getDbName());
                driverClass = "org.postgresql.Driver";
            } else {
                System.out.println("不支持的数据库类型: " + dbType);
                return null;
            }
            
            // 创建数据库连接
            System.out.println("尝试连接到数据库: " + urlBuilder.toString() + ", 用户名: " + database.getDbUsername());
            Class.forName(driverClass);
            Connection conn = DriverManager.getConnection(urlBuilder.toString(), 
                    database.getDbUsername(), database.getDbPassword());
            System.out.println("数据库连接成功");
            return conn;
        } catch (Exception e) {
            System.out.println("连接数据库失败: " + e.getMessage());
            e.printStackTrace();
            return null;
        }
    }
    
    /**
     * 关闭数据库连接和相关资源
     */
    private void closeConnection(Connection conn, Statement stmt, ResultSet rs) {
        try {
            if (rs != null) {
                rs.close();
            }
            if (stmt != null) {
                stmt.close();
            }
            if (conn != null) {
                conn.close();
            }
        } catch (SQLException e) {
            e.printStackTrace();
        }
    }

    /**
     * 数据库字段信息类
     */
    private class ColumnInfo {
        private String name;
        private String type;
        private int size;
        private int decimalDigits;
        private boolean nullable;
        private String defaultValue;
        private boolean primaryKey;
        private boolean autoIncrement;
        
        public String getName() {
            return name;
        }
        
        public void setName(String name) {
            this.name = name;
        }
        
        public String getType() {
            return type;
        }
        
        public void setType(String type) {
            this.type = type;
        }
        
        public int getSize() {
            return size;
        }
        
        public void setSize(int size) {
            this.size = size;
        }
        
        public int getDecimalDigits() {
            return decimalDigits;
        }
        
        public void setDecimalDigits(int decimalDigits) {
            this.decimalDigits = decimalDigits;
        }
        
        public boolean isNullable() {
            return nullable;
        }
        
        public void setNullable(boolean nullable) {
            this.nullable = nullable;
        }
        
        public String getDefaultValue() {
            return defaultValue;
        }
        
        public void setDefaultValue(String defaultValue) {
            this.defaultValue = defaultValue;
        }
        
        public boolean isPrimaryKey() {
            return primaryKey;
        }
        
        public void setPrimaryKey(boolean primaryKey) {
            this.primaryKey = primaryKey;
        }
        
        public boolean isAutoIncrement() {
            return autoIncrement;
        }
        
        public void setAutoIncrement(boolean autoIncrement) {
            this.autoIncrement = autoIncrement;
        }
    }
} 