// 测试列类型处理方法
public class TestColumnType {
    
    public static String processColumnType(String columnType, int size, int decimalDigits) {
        String upperType = columnType.toUpperCase();
        
        // 处理INT UNSIGNED类型
        if (upperType.contains("INT") && upperType.contains("UNSIGNED")) {
            // 提取基础类型（INT, BIGINT, SMALLINT等）
            String baseType = upperType.replaceAll("\\s*UNSIGNED.*", "").trim();
            
            // 对于INT类型，在MySQL 8.0+中不建议使用显示宽度，直接返回 INT UNSIGNED
            if (baseType.equals("INT")) {
                return "INT UNSIGNED";
            } else if (baseType.equals("BIGINT")) {
                return "BIGINT UNSIGNED";
            } else if (baseType.equals("SMALLINT")) {
                return "SMALLINT UNSIGNED";
            } else if (baseType.equals("TINYINT")) {
                return "TINYINT UNSIGNED";
            } else if (baseType.equals("MEDIUMINT")) {
                return "MEDIUMINT UNSIGNED";
            }
        }
        
        // 处理其他数值类型
        if (upperType.contains("INT") && !upperType.contains("UNSIGNED")) {
            String baseType = upperType.replaceAll("\\(\\d+\\)", "").trim();
            if (baseType.equals("INT")) {
                return "INT";
            } else if (baseType.equals("BIGINT")) {
                return "BIGINT";
            } else if (baseType.equals("SMALLINT")) {
                return "SMALLINT";
            } else if (baseType.equals("TINYINT")) {
                return "TINYINT";
            } else if (baseType.equals("MEDIUMINT")) {
                return "MEDIUMINT";
            }
        }
        
        // 处理日期时间类型，不需要精度
        if (upperType.contains("DATETIME") || upperType.contains("TIMESTAMP") || 
            upperType.contains("DATE") || upperType.contains("TIME")) {
            return columnType.replaceAll("\\(\\d+\\)", "");
        }
        
        // 处理需要精度的类型（VARCHAR, CHAR, DECIMAL等）
        if (upperType.contains("VARCHAR") || upperType.contains("CHAR") || 
            upperType.contains("DECIMAL") || upperType.contains("NUMERIC") ||
            upperType.contains("FLOAT") || upperType.contains("DOUBLE")) {
            
            String baseType = columnType.replaceAll("\\(.*\\)", "");
            
            if (size > 0) {
                if (decimalDigits > 0 && (upperType.contains("DECIMAL") || upperType.contains("NUMERIC"))) {
                    return baseType + "(" + size + "," + decimalDigits + ")";
                } else {
                    return baseType + "(" + size + ")";
                }
            }
        }
        
        // 对于其他类型，直接返回原类型（去除可能的精度信息）
        return columnType.replaceAll("\\(\\d+\\)", "");
    }
    
    public static void main(String[] args) {
        // 测试问题中的类型
        System.out.println("测试 INT UNSIGNED:");
        System.out.println(processColumnType("INT UNSIGNED", 10, 0)); // 应该输出: INT UNSIGNED
        
        System.out.println("测试 INT(1) UNSIGNED:");
        System.out.println(processColumnType("INT(1) UNSIGNED", 1, 0)); // 应该输出: INT UNSIGNED
        
        System.out.println("测试 VARCHAR:");
        System.out.println(processColumnType("VARCHAR", 100, 0)); // 应该输出: VARCHAR(100)
        
        System.out.println("测试 DATETIME:");
        System.out.println(processColumnType("DATETIME", 0, 0)); // 应该输出: DATETIME
    }
}
