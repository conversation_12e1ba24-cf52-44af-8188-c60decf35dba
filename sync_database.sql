-- 数据同步-数据库表
drop table if exists sync_database;
create table sync_database (
  db_id           bigint(20)      not null auto_increment    comment '数据库ID',
  db_name         varchar(50)     not null                   comment '数据库名称',
  db_type         char(1)         not null                   comment '数据库类型（1 MySQL 2 Oracle 3 SQLServer 4 PostgreSQL）',
  server_id       bigint(20)      not null                   comment '所属服务器ID',
  server_name     varchar(50)     default null               comment '所属服务器名称',
  db_port         varchar(10)     not null                   comment '端口',
  db_username     varchar(30)     not null                   comment '用户名',
  db_password     varchar(100)    not null                   comment '密码',
  status          char(1)         default '0'                comment '状态（0正常 1停用）',
  create_by       varchar(64)     default ''                 comment '创建者',
  create_time     datetime                                   comment '创建时间',
  update_by       varchar(64)     default ''                 comment '更新者',
  update_time     datetime                                   comment '更新时间',
  remark          varchar(500)    default null               comment '备注',
  primary key (db_id)
) engine=innodb auto_increment=100 comment = '同步数据库表';

-- 菜单 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('数据库管理', (select menu_id from sys_menu where menu_name = '数据同步'), 2, 'database', 'datasync/database/index', 1, 0, 'C', '0', '0', 'system:database:list', 'database', 'admin', sysdate(), '', null, '数据库管理菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('数据库查询', @parentId, 1, '#', '', 1, 0, 'F', '0', '0', 'system:database:query', '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('数据库新增', @parentId, 2, '#', '', 1, 0, 'F', '0', '0', 'system:database:add', '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('数据库修改', @parentId, 3, '#', '', 1, 0, 'F', '0', '0', 'system:database:edit', '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('数据库删除', @parentId, 4, '#', '', 1, 0, 'F', '0', '0', 'system:database:remove', '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('数据库导出', @parentId, 5, '#', '', 1, 0, 'F', '0', '0', 'system:database:export', '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('数据库测试', @parentId, 6, '#', '', 1, 0, 'F', '0', '0', 'system:database:test', '#', 'admin', sysdate(), '', null, ''); 