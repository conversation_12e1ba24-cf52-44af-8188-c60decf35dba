package com.ruoyi.system.websocket;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Docker部署WebSocket
 * 用于实时显示Docker容器部署过程
 */
@ServerEndpoint("/websocket/docker/deploy/{userId}/{serverId}")
@Component
public class DockerDeployWebSocket {
    
    private static final Logger log = LoggerFactory.getLogger(DockerDeployWebSocket.class);
    
    /**
     * 记录当前在线连接数
     */
    private static AtomicInteger onlineCount = new AtomicInteger(0);
    
    /**
     * 存放所有在线的客户端
     * key格式: userId_serverId
     */
    private static Map<String, DockerDeployWebSocket> clients = new ConcurrentHashMap<>();
    
    /**
     * 与某个客户端的连接会话，需要通过它来给客户端发送数据
     */
    private Session session;
    
    /**
     * 当前用户ID
     */
    private String userId;
    
    /**
     * 当前服务器ID
     */
    private String serverId;
    
    /**
     * 连接建立成功调用的方法
     */
    @OnOpen
    public void onOpen(Session session, @PathParam("userId") String userId, @PathParam("serverId") String serverId) {
        this.session = session;
        this.userId = userId;
        this.serverId = serverId;
        
        // 将当前websocket对象存入map
        String key = userId + "_" + serverId;
        clients.put(key, this);
        
        // 在线数加1
        addOnlineCount();
        
        log.info("有新连接加入！当前在线人数为：{}, userId={}, serverId={}", getOnlineCount(), userId, serverId);
        
        try {
            sendMessage("连接成功，等待部署操作...");
        } catch (IOException e) {
            log.error("websocket连接异常", e);
        }
    }
    
    /**
     * 连接关闭调用的方法
     */
    @OnClose
    public void onClose() {
        // 从map中删除
        String key = userId + "_" + serverId;
        clients.remove(key);
        
        // 在线数减1
        subOnlineCount();
        
        log.info("有一连接关闭！当前在线人数为：{}", getOnlineCount());
    }
    
    /**
     * 收到客户端消息后调用的方法
     * 
     * @param message 客户端发送过来的消息
     */
    @OnMessage
    public void onMessage(String message, Session session) {
        log.info("收到来自客户端的消息：{}", message);
        
        // 这里可以处理客户端发来的消息，如取消部署等操作
    }
    
    /**
     * 发生错误时调用
     */
    @OnError
    public void onError(Session session, Throwable error) {
        log.error("发生错误", error);
    }
    
    /**
     * 发送消息
     */
    public void sendMessage(String message) throws IOException {
        this.session.getBasicRemote().sendText(message);
    }
    
    /**
     * 发送部署进度消息给指定用户的指定服务器连接
     * 
     * @param userId 用户ID
     * @param serverId 服务器ID
     * @param message 消息内容
     */
    public static void sendDeployMessage(String userId, String serverId, String message) {
        String key = userId + "_" + serverId;
        DockerDeployWebSocket client = clients.get(key);
        if (client != null) {
            try {
                client.sendMessage(message);
            } catch (IOException e) {
                log.error("发送消息异常", e);
            }
        } else {
            log.warn("用户{}的服务器{}连接不存在", userId, serverId);
        }
    }
    
    /**
     * 发送部署开始消息
     */
    public static void sendDeployStart(String userId, String serverId, String templateName) {
        sendDeployMessage(userId, serverId, "[开始] 正在部署容器: " + templateName);
    }
    
    /**
     * 发送命令执行消息
     */
    public static void sendCommandExecute(String userId, String serverId, String command) {
        sendDeployMessage(userId, serverId, "[命令] 执行: " + command);
    }
    
    /**
     * 发送命令输出消息
     */
    public static void sendCommandOutput(String userId, String serverId, String output) {
        sendDeployMessage(userId, serverId, "[输出] " + output);
    }
    
    /**
     * 发送部署完成消息
     */
    public static void sendDeployComplete(String userId, String serverId, boolean success, String message) {
        String status = success ? "成功" : "失败";
        sendDeployMessage(userId, serverId, "[完成] 部署" + status + ": " + message);
    }
    
    /**
     * 获取在线人数
     */
    public static int getOnlineCount() {
        return onlineCount.get();
    }
    
    /**
     * 在线人数加1
     */
    public static void addOnlineCount() {
        onlineCount.incrementAndGet();
    }
    
    /**
     * 在线人数减1
     */
    public static void subOnlineCount() {
        onlineCount.decrementAndGet();
    }
} 