package com.ruoyi.system.service;

import java.util.List;
import java.util.Map;
import com.ruoyi.system.domain.datasync.SyncDatabase;

/**
 * 数据库Service接口
 * 
 * <AUTHOR>
 */
public interface ISyncDatabaseService 
{
    /**
     * 查询数据库
     * 
     * @param dbId 数据库主键
     * @return 数据库
     */
    public SyncDatabase selectSyncDatabaseByDbId(Long dbId);

    /**
     * 查询数据库列表
     * 
     * @param syncDatabase 数据库
     * @return 数据库集合
     */
    public List<SyncDatabase> selectSyncDatabaseList(SyncDatabase syncDatabase);

    /**
     * 新增数据库
     * 
     * @param syncDatabase 数据库
     * @return 结果
     */
    public int insertSyncDatabase(SyncDatabase syncDatabase);

    /**
     * 修改数据库
     * 
     * @param syncDatabase 数据库
     * @return 结果
     */
    public int updateSyncDatabase(SyncDatabase syncDatabase);

    /**
     * 批量删除数据库
     * 
     * @param dbIds 需要删除的数据库主键集合
     * @return 结果
     */
    public int deleteSyncDatabaseByDbIds(Long[] dbIds);

    /**
     * 删除数据库信息
     * 
     * @param dbId 数据库主键
     * @return 结果
     */
    public int deleteSyncDatabaseByDbId(Long dbId);

    /**
     * 测试数据库连接
     * 
     * @param dbId 数据库主键
     * @return 结果
     */
    public boolean testDatabaseConnection(Long dbId);

    /**
     * 获取所有可用数据库列表（下拉选项用）
     * 
     * @return 数据库选项列表
     */
    public List<SyncDatabase> selectDatabaseOptions();
    
    /**
     * 本地备份数据库
     * 
     * @param dbId 数据库主键
     * @return 备份文件路径
     */
    public String backupDatabase(Long dbId);
    
    /**
     * 远程备份数据库
     * 
     * @param dbId 数据库主键
     * @param targetServerId 目标服务器ID
     * @param backupPath 备份路径，为空则使用默认路径
     * @return 备份文件路径
     */
    public String remoteBackupDatabase(Long dbId, Long targetServerId, String backupPath);
    
    /**
     * 获取数据库表结构
     * 
     * @param dbId 数据库主键
     * @return 表结构列表
     */
    public List<Map<String, Object>> getDatabaseTables(Long dbId);
    
    /**
     * 获取表字段结构
     * 
     * @param dbId 数据库主键
     * @param tableName 表名
     * @return 字段结构列表
     */
    public List<Map<String, Object>> getTableColumns(Long dbId, String tableName);
    
    /**
     * 执行SQL查询
     * 
     * @param dbId 数据库主键
     * @param sql SQL语句
     * @return 查询结果
     */
    public Map<String, Object> executeQuery(Long dbId, String sql);

    /**
     * 更新数据库定时备份设置
     * 
     * @param database 数据库
     * @return 结果
     */
    public int updateDatabaseBackupSettings(SyncDatabase database);

    /**
     * 执行定时备份任务
     */
    public void executeScheduledBackup();
} 