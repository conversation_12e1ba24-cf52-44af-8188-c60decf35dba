-- Elasticsearch模板（修复环境变量问题）
INSERT INTO sys_docker_template (
    template_name, template_description, image_name, image_tag, 
    port_mappings, volume_mappings, environment_vars, network_mode, 
    restart_policy, command, is_system, use_compose, 
    create_by, create_time, remark
) VALUES (
    'Elasticsearch搜索引擎(修复版)', 
    'Elasticsearch是一个分布式、RESTful风格的搜索和数据分析引擎，修复了环境变量问题',
    'elasticsearch', 
    '7.17.9', 
    '9200:9200,9300:9300', 
    '/data/elasticsearch/data:/usr/share/elasticsearch/data', 
    'discovery.type=single-node,ES_JAVA_OPTS="-Xms512m -Xmx512m",xpack.security.enabled=false', 
    'bridge', 
    'always', 
    NULL, 
    '1', 
    '0', 
    'admin', 
    sysdate(), 
    'Elasticsearch 7.17.9，默认端口9200，修复了环境变量问题'
);

-- 部署说明
/*
修复版Elasticsearch模板说明：

错误分析：
您遇到的错误 "unknown shorthand flag: 'X' in -Xmx512m" 是因为Docker将-Xms和-Xmx参数误认为是Docker命令的参数，
而不是传递给Elasticsearch的Java选项。

解决方案：
1. 此模板修改了环境变量的格式，将Java内存参数用引号括起来：ES_JAVA_OPTS="-Xms512m -Xmx512m"
2. 使用bridge网络模式代替host模式，避免可能的网络问题

部署前准备：
1. 确保目录存在：
   mkdir -p /data/elasticsearch/data
   chmod -R 777 /data/elasticsearch

2. 部署后访问：
   - 地址：http://服务器IP:9200
   - 检查是否返回正确的JSON响应

3. 重要提示：
   如果仍然无法部署，尝试使用以下手动命令：
   
   docker run -d --name es \
     -p 9200:9200 -p 9300:9300 \
     -v /data/elasticsearch/data:/usr/share/elasticsearch/data \
     -e "discovery.type=single-node" \
     -e "ES_JAVA_OPTS=-Xms512m -Xmx512m" \
     -e "xpack.security.enabled=false" \
     --network bridge \
     --restart always \
     elasticsearch:7.17.9
     
   注意：Elasticsearch需要较大的内存和虚拟内存，如果启动失败，请检查系统资源：
   1. 检查内存：free -m
   2. 设置虚拟内存：sysctl -w vm.max_map_count=262144
*/ 