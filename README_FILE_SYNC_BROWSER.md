# 文件同步模块目录导航功能

## 功能概述

文件同步模块的目录导航功能允许用户通过图形化界面浏览服务器目录结构，更直观地选择源目录和目标目录，无需手动输入完整路径，从而大大提高了操作的便捷性和准确性。

## 主要功能

1. **源目录浏览**：通过图形化界面选择源服务器上的目录
2. **目标目录浏览**：通过图形化界面选择目标服务器上的目录
3. **目录导航**：支持在目录树中上下导航，查看文件和子目录
4. **快速选择**：提供"选择当前目录"和"选择此目录"按钮，方便用户快速确认选择

## 使用说明

### 选择源目录

1. 在文件同步任务编辑页面，首先选择源服务器
2. 点击源目录输入框旁边的"浏览"按钮
3. 系统会弹出文件浏览器对话框，显示源服务器上的文件和目录
4. 通过点击目录名称进入子目录，通过点击返回按钮返回上级目录
5. 找到合适的目录后，点击"选择此目录"按钮或"选择当前目录"按钮确认选择
6. 系统将自动填充源目录路径到对应的输入框中

### 选择目标目录

1. 在文件同步任务编辑页面，选择目标服务器
2. 点击目标目录输入框旁边的"浏览"按钮
3. 系统会弹出文件浏览器对话框，显示目标服务器上的文件和目录
4. 通过点击目录名称进入子目录，通过点击返回按钮返回上级目录
5. 找到合适的目录后，点击"选择此目录"按钮或"选择当前目录"按钮确认选择
6. 系统将自动填充目标目录路径到对应的输入框中

## 操作指南

### 目录导航

- **进入子目录**：点击目录名称链接
- **返回上级目录**：点击文件浏览器顶部的返回按钮
- **刷新当前目录**：点击"刷新"按钮
- **选择当前目录**：点击底部的"选择当前目录"按钮
- **选择特定目录**：点击目录行右侧的"选择此目录"按钮

### 注意事项

1. 必须先选择服务器，才能打开文件浏览器
2. 文件浏览器只显示目录结构，不会显示文件内容
3. 目录路径将以服务器实际路径格式显示（如Linux系统为"/"分隔）
4. 文件浏览器默认显示所选服务器的根目录（"/"）
5. 如果之前已经输入了路径，文件浏览器会尝试导航到该路径

## 技术实现

该功能基于服务器文件浏览功能实现，主要通过以下API和组件：

- `listServerFiles`: 获取服务器上指定目录的文件和子目录列表
- `el-dialog`: 显示文件浏览器对话框
- `el-table`: 显示文件和目录列表
- `el-link`: 提供目录导航链接

## 使用建议

1. 使用文件浏览器选择目录比手动输入更准确，避免路径错误
2. 对于经常使用的目录，可以先浏览到合适的位置，然后选择
3. 选择目录时注意权限问题，确保源服务器和目标服务器对所选目录有相应的读写权限
4. 在创建任务前，建议先通过文件浏览器查看确认源和目标目录的内容 