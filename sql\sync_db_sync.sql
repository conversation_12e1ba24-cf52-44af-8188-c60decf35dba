-- 数据同步-数据库同步表
drop table if exists sync_db_sync;
create table sync_db_sync (
  sync_id         bigint(20)      not null auto_increment    comment '同步ID',
  sync_name       varchar(50)     not null                   comment '同步任务名称',
  source_db_id    bigint(20)      not null                   comment '源数据库ID',
  source_db_name  varchar(50)     default null               comment '源数据库名称',
  target_db_id    bigint(20)      not null                   comment '目标数据库ID',
  target_db_name  varchar(50)     default null               comment '目标数据库名称',
  sync_type       char(1)         not null                   comment '同步类型（1全量同步 2增量同步）',
  sync_tables     varchar(500)    default null               comment '同步表',
  exec_cycle      char(1)         default '0'                comment '执行周期（0手动 1每天 2每周 3每月）',
  exec_time       varchar(20)     default null               comment '执行时间',
  last_exec_time  varchar(20)     default null               comment '最近执行时间',
  next_exec_time  varchar(20)     default null               comment '下次执行时间',
  status          char(1)         default '0'                comment '状态（0正常 1停用）',
  create_by       varchar(64)     default ''                 comment '创建者',
  create_time     datetime                                   comment '创建时间',
  update_by       varchar(64)     default ''                 comment '更新者',
  update_time     datetime                                   comment '更新时间',
  remark          varchar(500)    default null               comment '备注',
  primary key (sync_id)
) engine=innodb auto_increment=100 comment = '数据库同步表';

-- 菜单 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('数据库同步', (select menu_id from sys_menu where menu_name = '数据同步'), 3, 'dbsync', 'datasync/dbsync/index', 1, 0, 'C', '0', '0', 'datasync:dbsync:list', 'database', 'admin', sysdate(), '', null, '数据库同步菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('数据库同步查询', @parentId, 1, '#', '', 1, 0, 'F', '0', '0', 'datasync:dbsync:query', '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('数据库同步新增', @parentId, 2, '#', '', 1, 0, 'F', '0', '0', 'datasync:dbsync:add', '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('数据库同步修改', @parentId, 3, '#', '', 1, 0, 'F', '0', '0', 'datasync:dbsync:edit', '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('数据库同步删除', @parentId, 4, '#', '', 1, 0, 'F', '0', '0', 'datasync:dbsync:remove', '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('数据库同步导出', @parentId, 5, '#', '', 1, 0, 'F', '0', '0', 'datasync:dbsync:export', '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('数据库同步执行', @parentId, 6, '#', '', 1, 0, 'F', '0', '0', 'datasync:dbsync:execute', '#', 'admin', sysdate(), '', null, ''); 