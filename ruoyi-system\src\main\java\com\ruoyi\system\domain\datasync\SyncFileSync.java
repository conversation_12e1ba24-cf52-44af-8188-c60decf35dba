package com.ruoyi.system.domain.datasync;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 文件同步对象 sync_file_sync
 * 
 * <AUTHOR>
 */
public class SyncFileSync extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 任务ID */
    @Excel(name = "任务ID")
    private Long taskId;

    /** 任务名称 */
    @Excel(name = "任务名称")
    private String taskName;

    /** 源服务器ID */
    @Excel(name = "源服务器ID")
    private Long sourceServerId;

    /** 源服务器名称 */
    @Excel(name = "源服务器名称")
    private String sourceServerName;

    /** 源路径 */
    @Excel(name = "源路径")
    private String sourcePath;

    /** 目标服务器ID */
    @Excel(name = "目标服务器ID")
    private Long targetServerId;

    /** 目标服务器名称 */
    @Excel(name = "目标服务器名称")
    private String targetServerName;

    /** 目标路径 */
    @Excel(name = "目标路径")
    private String targetPath;

    /** 同步类型（1全量同步 2增量同步） */
    @Excel(name = "同步类型", readConverterExp = "1=全量同步,2=增量同步")
    private String syncType;

    /** 执行周期（0手动执行 1每天 2每周 3每月） */
    @Excel(name = "执行周期", readConverterExp = "0=手动执行,1=每天,2=每周,3=每月")
    private String execCycle;

    /** 执行时间 */
    @Excel(name = "执行时间")
    private String execTime;

    /** 最近执行时间 */
    @Excel(name = "最近执行时间")
    private String lastExecTime;

    /** 下次执行时间 */
    @Excel(name = "下次执行时间")
    private String nextExecTime;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    public void setTaskId(Long taskId) 
    {
        this.taskId = taskId;
    }

    public Long getTaskId() 
    {
        return taskId;
    }
    public void setTaskName(String taskName) 
    {
        this.taskName = taskName;
    }

    public String getTaskName() 
    {
        return taskName;
    }
    public void setSourceServerId(Long sourceServerId) 
    {
        this.sourceServerId = sourceServerId;
    }

    public Long getSourceServerId() 
    {
        return sourceServerId;
    }
    public void setSourceServerName(String sourceServerName) 
    {
        this.sourceServerName = sourceServerName;
    }

    public String getSourceServerName() 
    {
        return sourceServerName;
    }
    public void setSourcePath(String sourcePath) 
    {
        this.sourcePath = sourcePath;
    }

    public String getSourcePath() 
    {
        return sourcePath;
    }
    public void setTargetServerId(Long targetServerId) 
    {
        this.targetServerId = targetServerId;
    }

    public Long getTargetServerId() 
    {
        return targetServerId;
    }
    public void setTargetServerName(String targetServerName) 
    {
        this.targetServerName = targetServerName;
    }

    public String getTargetServerName() 
    {
        return targetServerName;
    }
    public void setTargetPath(String targetPath) 
    {
        this.targetPath = targetPath;
    }

    public String getTargetPath() 
    {
        return targetPath;
    }
    public void setSyncType(String syncType) 
    {
        this.syncType = syncType;
    }

    public String getSyncType() 
    {
        return syncType;
    }
    public void setExecCycle(String execCycle) 
    {
        this.execCycle = execCycle;
    }

    public String getExecCycle() 
    {
        return execCycle;
    }
    public void setExecTime(String execTime) 
    {
        this.execTime = execTime;
    }

    public String getExecTime() 
    {
        return execTime;
    }
    public void setLastExecTime(String lastExecTime) 
    {
        this.lastExecTime = lastExecTime;
    }

    public String getLastExecTime() 
    {
        return lastExecTime;
    }
    public void setNextExecTime(String nextExecTime) 
    {
        this.nextExecTime = nextExecTime;
    }

    public String getNextExecTime() 
    {
        return nextExecTime;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("taskId", getTaskId())
            .append("taskName", getTaskName())
            .append("sourceServerId", getSourceServerId())
            .append("sourceServerName", getSourceServerName())
            .append("sourcePath", getSourcePath())
            .append("targetServerId", getTargetServerId())
            .append("targetServerName", getTargetServerName())
            .append("targetPath", getTargetPath())
            .append("syncType", getSyncType())
            .append("execCycle", getExecCycle())
            .append("execTime", getExecTime())
            .append("lastExecTime", getLastExecTime())
            .append("nextExecTime", getNextExecTime())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
} 