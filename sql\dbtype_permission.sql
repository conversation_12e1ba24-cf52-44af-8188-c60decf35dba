-- 为管理员角色添加数据库类型管理的权限

-- 获取管理员角色ID
select @adminRoleId := role_id from sys_role where role_key = 'admin';

-- 获取数据库类型管理菜单ID
select @dbtypeMenuId := menu_id from sys_menu where menu_name = '数据库类型';

-- 插入角色-菜单权限记录
insert into sys_role_menu values (@adminRoleId, @dbtypeMenuId);

-- 获取子菜单ID
select @queryId := menu_id from sys_menu where menu_name = '数据库类型查询' and parent_id = @dbtypeMenuId;
select @addId := menu_id from sys_menu where menu_name = '数据库类型新增' and parent_id = @dbtypeMenuId;
select @editId := menu_id from sys_menu where menu_name = '数据库类型修改' and parent_id = @dbtypeMenuId;
select @removeId := menu_id from sys_menu where menu_name = '数据库类型删除' and parent_id = @dbtypeMenuId;
select @exportId := menu_id from sys_menu where menu_name = '数据库类型导出' and parent_id = @dbtypeMenuId;

-- 插入子菜单权限
insert into sys_role_menu values (@adminRoleId, @queryId);
insert into sys_role_menu values (@adminRoleId, @addId);
insert into sys_role_menu values (@adminRoleId, @editId);
insert into sys_role_menu values (@adminRoleId, @removeId);
insert into sys_role_menu values (@adminRoleId, @exportId); 