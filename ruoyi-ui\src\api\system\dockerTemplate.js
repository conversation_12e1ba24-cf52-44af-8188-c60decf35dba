import request from '@/utils/request'

// 查询Docker容器模板列表
export function listDockerTemplate(query) {
  return request({
    url: '/system/docker/template/list',
    method: 'get',
    params: query
  })
}

// 查询Docker容器模板详细
export function getDockerTemplate(templateId) {
  return request({
    url: '/system/docker/template/' + templateId,
    method: 'get'
  })
}

// 新增Docker容器模板
export function addDockerTemplate(data) {
  return request({
    url: '/system/docker/template',
    method: 'post',
    data: data
  })
}

// 修改Docker容器模板
export function updateDockerTemplate(data) {
  return request({
    url: '/system/docker/template',
    method: 'put',
    data: data
  })
}

// 删除Docker容器模板
export function delDockerTemplate(templateId) {
  return request({
    url: '/system/docker/template/' + templateId,
    method: 'delete'
  })
}

// 导出Docker容器模板
export function exportDockerTemplate(query) {
  return request({
    url: '/system/docker/template/export',
    method: 'get',
    params: query
  })
}

// 从模板创建Docker容器
export function createContainerFromTemplate(serverId, templateId, data) {
  return request({
    url: `/system/docker/template/create/${serverId}/${templateId}`,
    method: 'post',
    data: data
  })
}

// 获取Docker容器部署日志
export function getDeployLogs(serverId, processId) {
  return request({
    url: `/system/docker/template/deploy/logs/${serverId}/${processId}`,
    method: 'get'
  })
} 