-- 数据库类型字典
insert into sys_dict_type(dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)
values('数据库类型', 'sys_db_type', '0', 'admin', sysdate(), '', null, '数据库类型列表');

-- 字典数据
insert into sys_dict_data(dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
values(1, 'MySQL', '1', 'sys_db_type', '', 'primary', 'Y', '0', 'admin', sysdate(), '', null, 'MySQL数据库');

insert into sys_dict_data(dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
values(2, 'Oracle', '2', 'sys_db_type', '', 'success', 'N', '0', 'admin', sysdate(), '', null, 'Oracle数据库');

insert into sys_dict_data(dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
values(3, 'SQLServer', '3', 'sys_db_type', '', 'info', 'N', '0', 'admin', sysdate(), '', null, 'SQLServer数据库');

insert into sys_dict_data(dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
values(4, 'PostgreSQL', '4', 'sys_db_type', '', 'warning', 'N', '0', 'admin', sysdate(), '', null, 'PostgreSQL数据库'); 