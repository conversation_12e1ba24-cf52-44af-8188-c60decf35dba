<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SysDockerTemplateMapper">
    
    <resultMap type="SysDockerTemplate" id="DockerTemplateResult">
        <id     property="templateId"          column="template_id"          />
        <result property="templateName"        column="template_name"        />
        <result property="templateDescription" column="template_description" />
        <result property="imageName"           column="image_name"           />
        <result property="imageTag"            column="image_tag"            />
        <result property="portMappings"        column="port_mappings"        />
        <result property="volumeMappings"      column="volume_mappings"      />
        <result property="environmentVars"     column="environment_vars"     />
        <result property="networkMode"         column="network_mode"         />
        <result property="restartPolicy"       column="restart_policy"       />
        <result property="command"             column="command"              />
        <result property="isSystem"            column="is_system"            />
        <result property="useCompose"          column="use_compose"          />
        <result property="composeConfig"       column="compose_config"       />
        <result property="createBy"            column="create_by"            />
        <result property="createTime"          column="create_time"          />
        <result property="updateBy"            column="update_by"            />
        <result property="updateTime"          column="update_time"          />
        <result property="remark"              column="remark"               />
    </resultMap>

    <sql id="selectDockerTemplateVo">
        select template_id, template_name, template_description, image_name, image_tag, port_mappings, volume_mappings, 
        environment_vars, network_mode, restart_policy, command, is_system, use_compose, compose_config, create_by, create_time, update_by, update_time, remark
        from sys_docker_template
    </sql>

    <select id="selectDockerTemplateList" parameterType="SysDockerTemplate" resultMap="DockerTemplateResult">
        <include refid="selectDockerTemplateVo"/>
        <where>
            <if test="templateName != null and templateName != ''">
                AND template_name like concat('%', #{templateName}, '%')
            </if>
            <if test="templateDescription != null and templateDescription != ''">
                AND template_description like concat('%', #{templateDescription}, '%')
            </if>
            <if test="imageName != null and imageName != ''">
                AND image_name like concat('%', #{imageName}, '%')
            </if>
            <if test="imageTag != null and imageTag != ''">
                AND image_tag = #{imageTag}
            </if>
            <if test="isSystem != null and isSystem != ''">
                AND is_system = #{isSystem}
            </if>
        </where>
    </select>
    
    <select id="selectDockerTemplateById" parameterType="Long" resultMap="DockerTemplateResult">
        <include refid="selectDockerTemplateVo"/>
        where template_id = #{templateId}
    </select>
        
    <insert id="insertDockerTemplate" parameterType="SysDockerTemplate" useGeneratedKeys="true" keyProperty="templateId">
        insert into sys_docker_template
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="templateName != null and templateName != ''">template_name,</if>
            <if test="templateDescription != null">template_description,</if>
            <if test="imageName != null and imageName != ''">image_name,</if>
            <if test="imageTag != null and imageTag != ''">image_tag,</if>
            <if test="portMappings != null">port_mappings,</if>
            <if test="volumeMappings != null">volume_mappings,</if>
            <if test="environmentVars != null">environment_vars,</if>
            <if test="networkMode != null and networkMode != ''">network_mode,</if>
            <if test="restartPolicy != null and restartPolicy != ''">restart_policy,</if>
            <if test="command != null">command,</if>
            <if test="isSystem != null and isSystem != ''">is_system,</if>
            <if test="useCompose != null and useCompose != ''">use_compose,</if>
            <if test="composeConfig != null">compose_config,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="templateName != null and templateName != ''">#{templateName},</if>
            <if test="templateDescription != null">#{templateDescription},</if>
            <if test="imageName != null and imageName != ''">#{imageName},</if>
            <if test="imageTag != null and imageTag != ''">#{imageTag},</if>
            <if test="portMappings != null">#{portMappings},</if>
            <if test="volumeMappings != null">#{volumeMappings},</if>
            <if test="environmentVars != null">#{environmentVars},</if>
            <if test="networkMode != null and networkMode != ''">#{networkMode},</if>
            <if test="restartPolicy != null and restartPolicy != ''">#{restartPolicy},</if>
            <if test="command != null">#{command},</if>
            <if test="isSystem != null and isSystem != ''">#{isSystem},</if>
            <if test="useCompose != null and useCompose != ''">#{useCompose},</if>
            <if test="composeConfig != null">#{composeConfig},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateDockerTemplate" parameterType="SysDockerTemplate">
        update sys_docker_template
        <trim prefix="SET" suffixOverrides=",">
            <if test="templateName != null and templateName != ''">template_name = #{templateName},</if>
            <if test="templateDescription != null">template_description = #{templateDescription},</if>
            <if test="imageName != null and imageName != ''">image_name = #{imageName},</if>
            <if test="imageTag != null and imageTag != ''">image_tag = #{imageTag},</if>
            <if test="portMappings != null">port_mappings = #{portMappings},</if>
            <if test="volumeMappings != null">volume_mappings = #{volumeMappings},</if>
            <if test="environmentVars != null">environment_vars = #{environmentVars},</if>
            <if test="networkMode != null and networkMode != ''">network_mode = #{networkMode},</if>
            <if test="restartPolicy != null and restartPolicy != ''">restart_policy = #{restartPolicy},</if>
            <if test="command != null">command = #{command},</if>
            <if test="isSystem != null and isSystem != ''">is_system = #{isSystem},</if>
            <if test="useCompose != null and useCompose != ''">use_compose = #{useCompose},</if>
            <if test="composeConfig != null">compose_config = #{composeConfig},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where template_id = #{templateId}
    </update>

    <delete id="deleteDockerTemplateById" parameterType="Long">
        delete from sys_docker_template where template_id = #{templateId}
    </delete>

    <delete id="deleteDockerTemplateByIds" parameterType="Long">
        delete from sys_docker_template where template_id in 
        <foreach collection="array" item="templateId" open="(" separator="," close=")">
            #{templateId}
        </foreach>
    </delete>
</mapper> 