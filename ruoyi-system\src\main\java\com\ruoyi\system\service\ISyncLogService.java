package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.datasync.SyncLog;

/**
 * 同步日志Service接口
 * 
 * <AUTHOR>
 */
public interface ISyncLogService 
{
    /**
     * 查询同步日志
     * 
     * @param logId 同步日志主键
     * @return 同步日志
     */
    public SyncLog selectSyncLogByLogId(Long logId);

    /**
     * 查询同步日志列表
     * 
     * @param syncLog 同步日志
     * @return 同步日志集合
     */
    public List<SyncLog> selectSyncLogList(SyncLog syncLog);

    /**
     * 查询指定同步任务的日志列表
     * 
     * @param syncId 同步任务ID
     * @return 同步日志集合
     */
    public List<SyncLog> selectSyncLogBySyncId(Long syncId);

    /**
     * 新增同步日志
     * 
     * @param syncLog 同步日志
     * @return 结果
     */
    public int insertSyncLog(SyncLog syncLog);

    /**
     * 删除同步日志
     * 
     * @param logId 同步日志主键
     * @return 结果
     */
    public int deleteSyncLogByLogId(Long logId);

    /**
     * 批量删除同步日志
     * 
     * @param logIds 需要删除的同步日志主键集合
     * @return 结果
     */
    public int deleteSyncLogByLogIds(Long[] logIds);
    
    /**
     * 删除指定同步任务的日志
     * 
     * @param syncId 同步任务ID
     * @return 结果
     */
    public int deleteSyncLogBySyncId(Long syncId);
} 