FROM maven:3.8-openjdk-8 AS builder

WORKDIR /app

# 复制整个项目
COPY . .

# 配置Maven使用阿里云镜像
RUN mkdir -p /root/.m2 \
    && echo '<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 https://maven.apache.org/xsd/settings-1.0.0.xsd">\
    <mirrors>\
        <mirror>\
            <id>aliyunmaven</id>\
            <mirrorOf>*</mirrorOf>\
            <name>阿里云公共仓库</name>\
            <url>https://maven.aliyun.com/repository/public</url>\
        </mirror>\
    </mirrors>\
</settings>' > /root/.m2/settings.xml

# 编译打包
RUN mvn clean package -DskipTests

# 运行阶段
FROM openjdk:8-jdk

WORKDIR /app

# 安装字体和图形库支持
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    fontconfig \
    libfreetype6 \
    xfonts-base \
    xfonts-75dpi \
    libxrender1 \
    libxtst6 \
    libxi6 \
    libxext6 \
    libx11-6 \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# 创建配置目录
RUN mkdir -p /app/config

# 复制构建好的jar包
COPY --from=builder /app/ruoyi-admin/target/ruoyi-admin.jar ./app.jar

# 设置时区
ENV TZ=Asia/Shanghai

# 设置Java系统属性，解决字体问题
ENV JAVA_OPTS="-Djava.awt.headless=true -Dfile.encoding=UTF-8"

# 暴露端口
EXPOSE 8080

# 启动命令，只使用外部配置文件
ENTRYPOINT java $JAVA_OPTS -jar app.jar --spring.config.location=file:/app/config/application-prod.yml 