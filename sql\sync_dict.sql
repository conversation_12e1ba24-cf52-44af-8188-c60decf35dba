-- 数据同步类型字典
insert into sys_dict_type(dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)
values(100, '同步类型', 'sys_sync_type', '0', 'admin', sysdate(), '', null, '同步类型列表');

-- 同步类型字典数据
insert into sys_dict_data(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
values(1001, 1, '全量同步', '1', 'sys_sync_type', '', 'primary', 'Y', '0', 'admin', sysdate(), '', null, '全量同步');

insert into sys_dict_data(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
values(1002, 2, '增量同步', '2', 'sys_sync_type', '', 'success', 'N', '0', 'admin', sysdate(), '', null, '增量同步');

-- 执行周期字典
insert into sys_dict_type(dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)
values('执行周期', 'sys_exec_cycle', '0', 'admin', sysdate(), '', null, '执行周期列表');

-- 执行周期字典数据
insert into sys_dict_data(dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
values(1, '手动执行', '0', 'sys_exec_cycle', '', 'default', 'Y', '0', 'admin', sysdate(), '', null, '手动执行');

insert into sys_dict_data(dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
values(2, '每天', '1', 'sys_exec_cycle', '', 'primary', 'N', '0', 'admin', sysdate(), '', null, '每天执行');

insert into sys_dict_data(dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
values(3, '每周', '2', 'sys_exec_cycle', '', 'success', 'N', '0', 'admin', sysdate(), '', null, '每周执行');

insert into sys_dict_data(dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
values(4, '每月', '3', 'sys_exec_cycle', '', 'warning', 'N', '0', 'admin', sysdate(), '', null, '每月执行');

-- 同步方式字典
insert into sys_dict_type(dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)
values(101, '同步方式', 'sys_sync_mode', '0', 'admin', sysdate(), '', null, '同步方式列表');

-- 同步方式字典数据
insert into sys_dict_data(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
values(1101, 1, '全量同步', '1', 'sys_sync_mode', '', 'warning', 'Y', '0', 'admin', sysdate(), '', null, '全量同步，会清空目标表数据');

insert into sys_dict_data(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
values(1102, 2, '增量同步', '2', 'sys_sync_mode', '', 'success', 'N', '0', 'admin', sysdate(), '', null, '增量同步，仅同步变化数据'); 