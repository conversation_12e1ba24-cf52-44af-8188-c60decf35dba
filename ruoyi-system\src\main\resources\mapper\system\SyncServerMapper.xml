<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SyncServerMapper">
    
    <resultMap type="com.ruoyi.system.domain.datasync.SyncServer" id="SyncServerResult">
        <result property="serverId"    column="server_id"    />
        <result property="serverName"    column="server_name"    />
        <result property="ipAddress"    column="ip_address"    />
        <result property="port"    column="port"    />
        <result property="userName"    column="user_name"    />
        <result property="password"    column="password"    />
        <result property="backupPath"    column="backup_path"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectSyncServerVo">
        select server_id, server_name, ip_address, port, user_name, password, backup_path, status, create_by, create_time, update_by, update_time, remark from sync_server
    </sql>

    <select id="selectSyncServerList" parameterType="com.ruoyi.system.domain.datasync.SyncServer" resultMap="SyncServerResult">
        <include refid="selectSyncServerVo"/>
        <where>  
            <if test="serverName != null  and serverName != ''"> and server_name like concat('%', #{serverName}, '%')</if>
            <if test="ipAddress != null  and ipAddress != ''"> and ip_address like concat('%', #{ipAddress}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectSyncServerByServerId" parameterType="Long" resultMap="SyncServerResult">
        <include refid="selectSyncServerVo"/>
        where server_id = #{serverId}
    </select>
        
    <insert id="insertSyncServer" parameterType="com.ruoyi.system.domain.datasync.SyncServer" useGeneratedKeys="true" keyProperty="serverId">
        insert into sync_server
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="serverName != null">server_name,</if>
            <if test="ipAddress != null">ip_address,</if>
            <if test="port != null">port,</if>
            <if test="userName != null">user_name,</if>
            <if test="password != null">password,</if>
            <if test="backupPath != null">backup_path,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="serverName != null">#{serverName},</if>
            <if test="ipAddress != null">#{ipAddress},</if>
            <if test="port != null">#{port},</if>
            <if test="userName != null">#{userName},</if>
            <if test="password != null">#{password},</if>
            <if test="backupPath != null">#{backupPath},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateSyncServer" parameterType="com.ruoyi.system.domain.datasync.SyncServer">
        update sync_server
        <trim prefix="SET" suffixOverrides=",">
            <if test="serverName != null">server_name = #{serverName},</if>
            <if test="ipAddress != null">ip_address = #{ipAddress},</if>
            <if test="port != null">port = #{port},</if>
            <if test="userName != null">user_name = #{userName},</if>
            <if test="password != null">password = #{password},</if>
            <if test="backupPath != null">backup_path = #{backupPath},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where server_id = #{serverId}
    </update>

    <delete id="deleteSyncServerByServerId" parameterType="Long">
        delete from sync_server where server_id = #{serverId}
    </delete>

    <delete id="deleteSyncServerByServerIds" parameterType="String">
        delete from sync_server where server_id in 
        <foreach item="serverId" collection="array" open="(" separator="," close=")">
            #{serverId}
        </foreach>
    </delete>
</mapper> 