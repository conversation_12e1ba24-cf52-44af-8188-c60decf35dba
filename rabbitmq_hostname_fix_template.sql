-- RabbitMQ模板（修复hostname问题）
INSERT INTO sys_docker_template (
    template_name, template_description, image_name, image_tag, 
    port_mappings, volume_mappings, environment_vars, network_mode, 
    restart_policy, command, is_system, use_compose, 
    create_by, create_time, remark
) VALUES (
    'RabbitMQ消息队列(带插件-修复版)', 
    'RabbitMQ是实现了高级消息队列协议（AMQP）的开源消息代理软件，预装常用插件，修复hostname问题',
    'rabbitmq', 
    'management', 
    '5672:5672,15672:15672,1883:1883,61613:61613', 
    '/data/rabbitmq/data:/var/lib/rabbitmq', 
    'RABBITMQ_DEFAULT_USER=admin,RABBITMQ_DEFAULT_PASS=admin123,RABBITMQ_NODENAME=rabbit@rabbitmq', 
    'bridge', 
    'always', 
    'sh -c "rabbitmq-plugins enable rabbitmq_mqtt rabbitmq_federation rabbitmq_stomp rabbitmq_shovel && rabbitmq-server"', 
    '1', 
    '0', 
    'admin', 
    sysdate(), 
    'RabbitMQ服务，默认端口5672，管理界面15672，MQTT端口1883，STOMP端口61613，修复了hostname问题'
);

-- 部署说明
/*
修复版RabbitMQ模板说明：

错误分析：
您遇到的错误 "epmd error for host 192: badarg" 是因为RabbitMQ尝试将容器的主机名（可能是"192"）
作为Erlang节点名称的一部分，但这不是一个有效的主机名。

解决方案：
1. 此模板增加了环境变量 RABBITMQ_NODENAME=rabbit@rabbitmq 来指定正确的节点名称
2. 部署时系统会自动添加 --hostname rabbitmq 参数设置容器主机名

部署前准备：
1. 确保目录存在：
   mkdir -p /data/rabbitmq/data
   chmod -R 777 /data/rabbitmq

2. 部署后访问管理界面：
   - 地址：http://服务器IP:15672
   - 用户名：admin
   - 密码：admin123

3. 重要提示：
   如果仍然无法访问，尝试使用以下手动命令部署：
   
   docker run -d --name rabbitmq \
     --hostname rabbitmq \
     -p 5672:5672 -p 15672:15672 -p 1883:1883 -p 61613:61613 \
     -v /data/rabbitmq/data:/var/lib/rabbitmq \
     -e RABBITMQ_DEFAULT_USER=admin \
     -e RABBITMQ_DEFAULT_PASS=admin123 \
     -e RABBITMQ_NODENAME=rabbit@rabbitmq \
     --network bridge \
     --restart always \
     rabbitmq:management
     
   然后手动启用插件：
   docker exec -it rabbitmq rabbitmq-plugins enable rabbitmq_mqtt rabbitmq_federation rabbitmq_stomp rabbitmq_shovel
   docker restart rabbitmq
*/ 