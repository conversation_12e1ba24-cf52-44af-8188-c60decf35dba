import request from '@/utils/request'

// 查询数据库同步列表
export function listDbSync(query) {
  return request({
    url: '/datasync/dbsync/list',
    method: 'get',
    params: query
  })
}

// 查询数据库同步详细
export function getDbSync(syncId) {
  return request({
    url: '/datasync/dbsync/' + syncId,
    method: 'get'
  })
}

// 新增数据库同步
export function addDbSync(data) {
  return request({
    url: '/datasync/dbsync',
    method: 'post',
    data: data
  })
}

// 修改数据库同步
export function updateDbSync(data) {
  return request({
    url: '/datasync/dbsync',
    method: 'put',
    data: data
  })
}

// 删除数据库同步
export function delDbSync(syncId) {
  return request({
    url: '/datasync/dbsync/' + syncId,
    method: 'delete'
  })
}

// 执行数据库同步
export function executeDbSync(syncId) {
  return request({
    url: '/datasync/dbsync/execute/' + syncId,
    method: 'get'
  })
}

// 获取同步日志列表
export function listSyncLogs(syncId) {
  return request({
    url: '/datasync/dbsync/logs/' + syncId,
    method: 'get'
  })
}

// 获取同步日志详情
export function getSyncLogDetail(logId) {
  return request({
    url: '/datasync/dbsync/log/detail/' + logId,
    method: 'get'
  })
}

// 获取数据库表列表
export function getDatabaseTables(dbId) {
  return request({
    url: '/system/sync/database/tables/' + dbId,
    method: 'get'
  })
} 