-- 添加数据库同步定时任务
INSERT INTO sys_job (job_id, job_name, job_group, invoke_target, cron_expression, misfire_policy, concurrent, status, create_by, create_time, remark) 
VALUES (5, '数据库同步定时任务', 'DEFAULT', 'syncDbTask.executeScheduledTasks()', '0 0/5 * * * ?', '3', '1', '0', 'admin', SYSDATE(), '每5分钟执行一次，检查并执行到期的数据库同步任务');

-- 添加数据字典项
INSERT INTO sys_dict_data VALUES (30, 1, '手动同步', '0', 'sys_sync_type', '', 'info', 'Y', '0', 'admin', SYSDATE(), '', NULL, '手动触发的同步任务');
INSERT INTO sys_dict_data VALUES (31, 2, '自动同步', '1', 'sys_sync_type', '', 'primary', 'Y', '0', 'admin', SYSDATE(), '', NULL, '自动执行的同步任务');

INSERT INTO sys_dict_data VALUES (32, 1, '全量同步', '1', 'sys_sync_mode', '', 'warning', 'Y', '0', 'admin', SYSDATE(), '', NULL, '同步所有数据（会清空目标表）');
INSERT INTO sys_dict_data VALUES (33, 2, '增量同步', '2', 'sys_sync_mode', '', 'success', 'Y', '0', 'admin', SYSDATE(), '', NULL, '只同步变化的数据');

INSERT INTO sys_dict_data VALUES (34, 1, '手动执行', '0', 'sys_exec_cycle', '', 'info', 'Y', '0', 'admin', SYSDATE(), '', NULL, '手动触发执行');
INSERT INTO sys_dict_data VALUES (35, 2, '每天执行', '1', 'sys_exec_cycle', '', 'primary', 'Y', '0', 'admin', SYSDATE(), '', NULL, '每天定时执行');
INSERT INTO sys_dict_data VALUES (36, 3, '每周执行', '2', 'sys_exec_cycle', '', 'success', 'Y', '0', 'admin', SYSDATE(), '', NULL, '每周定时执行');
INSERT INTO sys_dict_data VALUES (37, 4, '每月执行', '3', 'sys_exec_cycle', '', 'warning', 'Y', '0', 'admin', SYSDATE(), '', NULL, '每月定时执行'); 