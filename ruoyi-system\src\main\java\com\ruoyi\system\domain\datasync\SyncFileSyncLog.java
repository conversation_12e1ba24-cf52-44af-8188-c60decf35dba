package com.ruoyi.system.domain.datasync;

import java.util.Date;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 文件同步日志对象 sync_file_sync_log
 * 
 * <AUTHOR>
 */
public class SyncFileSyncLog extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 日志ID */
    @Excel(name = "日志ID")
    private Long logId;

    /** 任务ID */
    @Excel(name = "任务ID")
    private Long taskId;

    /** 任务名称 */
    @Excel(name = "任务名称")
    private String taskName;

    /** 源服务器ID */
    @Excel(name = "源服务器ID")
    private Long sourceServerId;

    /** 源服务器名称 */
    @Excel(name = "源服务器名称")
    private String sourceServerName;

    /** 目标服务器ID */
    @Excel(name = "目标服务器ID")
    private Long targetServerId;

    /** 目标服务器名称 */
    @Excel(name = "目标服务器名称")
    private String targetServerName;

    /** 同步类型 */
    @Excel(name = "同步类型", readConverterExp = "1=全量同步,2=增量同步")
    private String syncType;

    /** 开始时间 */
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 结束时间 */
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 耗时(秒) */
    @Excel(name = "耗时(秒)")
    private Integer duration;

    /** 文件数量 */
    @Excel(name = "文件数量")
    private Integer fileCount;

    /** 总大小(字节) */
    @Excel(name = "总大小(字节)")
    private Long totalSize;

    /** 状态 */
    @Excel(name = "状态", readConverterExp = "0=成功,1=失败")
    private String status;

    /** 错误信息 */
    @Excel(name = "错误信息")
    private String errorInfo;

    public void setLogId(Long logId) 
    {
        this.logId = logId;
    }

    public Long getLogId() 
    {
        return logId;
    }
    public void setTaskId(Long taskId) 
    {
        this.taskId = taskId;
    }

    public Long getTaskId() 
    {
        return taskId;
    }
    public void setTaskName(String taskName) 
    {
        this.taskName = taskName;
    }

    public String getTaskName() 
    {
        return taskName;
    }
    public void setSourceServerId(Long sourceServerId) 
    {
        this.sourceServerId = sourceServerId;
    }

    public Long getSourceServerId() 
    {
        return sourceServerId;
    }
    public void setSourceServerName(String sourceServerName) 
    {
        this.sourceServerName = sourceServerName;
    }

    public String getSourceServerName() 
    {
        return sourceServerName;
    }
    public void setTargetServerId(Long targetServerId) 
    {
        this.targetServerId = targetServerId;
    }

    public Long getTargetServerId() 
    {
        return targetServerId;
    }
    public void setTargetServerName(String targetServerName) 
    {
        this.targetServerName = targetServerName;
    }

    public String getTargetServerName() 
    {
        return targetServerName;
    }
    public void setSyncType(String syncType) 
    {
        this.syncType = syncType;
    }

    public String getSyncType() 
    {
        return syncType;
    }
    public void setStartTime(Date startTime) 
    {
        this.startTime = startTime;
    }

    public Date getStartTime() 
    {
        return startTime;
    }
    public void setEndTime(Date endTime) 
    {
        this.endTime = endTime;
    }

    public Date getEndTime() 
    {
        return endTime;
    }
    public void setDuration(Integer duration) 
    {
        this.duration = duration;
    }

    public Integer getDuration() 
    {
        return duration;
    }
    public void setFileCount(Integer fileCount) 
    {
        this.fileCount = fileCount;
    }

    public Integer getFileCount() 
    {
        return fileCount;
    }
    public void setTotalSize(Long totalSize) 
    {
        this.totalSize = totalSize;
    }

    public Long getTotalSize() 
    {
        return totalSize;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setErrorInfo(String errorInfo) 
    {
        this.errorInfo = errorInfo;
    }

    public String getErrorInfo() 
    {
        return errorInfo;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("logId", getLogId())
            .append("taskId", getTaskId())
            .append("taskName", getTaskName())
            .append("sourceServerId", getSourceServerId())
            .append("sourceServerName", getSourceServerName())
            .append("targetServerId", getTargetServerId())
            .append("targetServerName", getTargetServerName())
            .append("syncType", getSyncType())
            .append("startTime", getStartTime())
            .append("endTime", getEndTime())
            .append("duration", getDuration())
            .append("fileCount", getFileCount())
            .append("totalSize", getTotalSize())
            .append("status", getStatus())
            .append("errorInfo", getErrorInfo())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("remark", getRemark())
            .toString();
    }
} 