-- RabbitMQ模板（预配置插件版本）
INSERT INTO sys_docker_template (
    template_name, template_description, image_name, image_tag, 
    port_mappings, volume_mappings, environment_vars, network_mode, 
    restart_policy, command, is_system, use_compose, 
    create_by, create_time, remark
) VALUES (
    'RabbitMQ消息队列(带插件)', 
    'RabbitMQ是实现了高级消息队列协议（AMQP）的开源消息代理软件，预装常用插件',
    'rabbitmq', 
    'management', 
    '5672:5672,15672:15672,1883:1883,61613:61613', 
    '/data/rabbitmq/data:/var/lib/rabbitmq,/data/rabbitmq/log:/var/log/rabbitmq,/data/rabbitmq/enabled_plugins:/etc/rabbitmq/enabled_plugins', 
    'RABBITMQ_DEFAULT_USER=admin,RABBITMQ_DEFAULT_PASS=admin123', 
    'bridge', 
    'always', 
    'sh -c "rabbitmq-plugins enable rabbitmq_mqtt rabbitmq_federation rabbitmq_stomp rabbitmq_shovel && rabbitmq-server"', 
    '1', 
    '0', 
    'admin', 
    sysdate(), 
    'RabbitMQ服务，默认端口5672，管理界面15672，已启用MQTT(1883)、STOMP(61613)、Federation和Shovel插件'
);

-- 创建enabled_plugins文件的说明
/*
在部署容器前，请先创建enabled_plugins文件：

cat > /data/rabbitmq/enabled_plugins << EOF
[rabbitmq_management,rabbitmq_mqtt,rabbitmq_federation,rabbitmq_stomp,rabbitmq_shovel].
EOF

这样容器启动时会自动加载这些插件。
*/ 