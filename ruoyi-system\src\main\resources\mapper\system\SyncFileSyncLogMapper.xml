<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SyncFileSyncLogMapper">
    
    <resultMap type="com.ruoyi.system.domain.datasync.SyncFileSyncLog" id="SyncFileSyncLogResult">
        <id property="logId" column="log_id"/>
        <result property="taskId" column="task_id"/>
        <result property="taskName" column="task_name"/>
        <result property="sourceServerId" column="source_server_id"/>
        <result property="sourceServerName" column="source_server_name"/>
        <result property="targetServerId" column="target_server_id"/>
        <result property="targetServerName" column="target_server_name"/>
        <result property="syncType" column="sync_type"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="duration" column="duration"/>
        <result property="fileCount" column="file_count"/>
        <result property="totalSize" column="total_size"/>
        <result property="status" column="status"/>
        <result property="errorInfo" column="error_info"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectSyncFileSyncLogVo">
        select log_id, task_id, task_name, source_server_id, source_server_name, target_server_id, target_server_name,
        sync_type, start_time, end_time, duration, file_count, total_size, status, error_info, create_by, create_time, remark
        from sync_file_sync_log
    </sql>

    <select id="selectSyncFileSyncLogList" parameterType="com.ruoyi.system.domain.datasync.SyncFileSyncLog" resultMap="SyncFileSyncLogResult">
        <include refid="selectSyncFileSyncLogVo"/>
        <where>  
            <if test="taskId != null "> and task_id = #{taskId}</if>
            <if test="taskName != null  and taskName != ''"> and task_name like concat('%', #{taskName}, '%')</if>
            <if test="sourceServerId != null "> and source_server_id = #{sourceServerId}</if>
            <if test="targetServerId != null "> and target_server_id = #{targetServerId}</if>
            <if test="syncType != null  and syncType != ''"> and sync_type = #{syncType}</if>
            <if test="startTime != null "> and start_time = #{startTime}</if>
            <if test="endTime != null "> and end_time = #{endTime}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectSyncFileSyncLogByLogId" parameterType="Long" resultMap="SyncFileSyncLogResult">
        <include refid="selectSyncFileSyncLogVo"/>
        where log_id = #{logId}
    </select>
    
    <select id="selectSyncFileSyncLogByTaskId" parameterType="Long" resultMap="SyncFileSyncLogResult">
        <include refid="selectSyncFileSyncLogVo"/>
        where task_id = #{taskId}
        order by create_time desc
    </select>
        
    <insert id="insertSyncFileSyncLog" parameterType="com.ruoyi.system.domain.datasync.SyncFileSyncLog" useGeneratedKeys="true" keyProperty="logId">
        insert into sync_file_sync_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskId != null">task_id,</if>
            <if test="taskName != null">task_name,</if>
            <if test="sourceServerId != null">source_server_id,</if>
            <if test="sourceServerName != null">source_server_name,</if>
            <if test="targetServerId != null">target_server_id,</if>
            <if test="targetServerName != null">target_server_name,</if>
            <if test="syncType != null">sync_type,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="duration != null">duration,</if>
            <if test="fileCount != null">file_count,</if>
            <if test="totalSize != null">total_size,</if>
            <if test="status != null">status,</if>
            <if test="errorInfo != null">error_info,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskId != null">#{taskId},</if>
            <if test="taskName != null">#{taskName},</if>
            <if test="sourceServerId != null">#{sourceServerId},</if>
            <if test="sourceServerName != null">#{sourceServerName},</if>
            <if test="targetServerId != null">#{targetServerId},</if>
            <if test="targetServerName != null">#{targetServerName},</if>
            <if test="syncType != null">#{syncType},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="duration != null">#{duration},</if>
            <if test="fileCount != null">#{fileCount},</if>
            <if test="totalSize != null">#{totalSize},</if>
            <if test="status != null">#{status},</if>
            <if test="errorInfo != null">#{errorInfo},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateSyncFileSyncLog" parameterType="com.ruoyi.system.domain.datasync.SyncFileSyncLog">
        update sync_file_sync_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskId != null">task_id = #{taskId},</if>
            <if test="taskName != null">task_name = #{taskName},</if>
            <if test="sourceServerId != null">source_server_id = #{sourceServerId},</if>
            <if test="sourceServerName != null">source_server_name = #{sourceServerName},</if>
            <if test="targetServerId != null">target_server_id = #{targetServerId},</if>
            <if test="targetServerName != null">target_server_name = #{targetServerName},</if>
            <if test="syncType != null">sync_type = #{syncType},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="duration != null">duration = #{duration},</if>
            <if test="fileCount != null">file_count = #{fileCount},</if>
            <if test="totalSize != null">total_size = #{totalSize},</if>
            <if test="status != null">status = #{status},</if>
            <if test="errorInfo != null">error_info = #{errorInfo},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where log_id = #{logId}
    </update>

    <delete id="deleteSyncFileSyncLogByLogId" parameterType="Long">
        delete from sync_file_sync_log where log_id = #{logId}
    </delete>

    <delete id="deleteSyncFileSyncLogByLogIds" parameterType="String">
        delete from sync_file_sync_log where log_id in 
        <foreach item="logId" collection="array" open="(" separator="," close=")">
            #{logId}
        </foreach>
    </delete>
    
    <delete id="deleteSyncFileSyncLogByTaskId" parameterType="Long">
        delete from sync_file_sync_log where task_id = #{taskId}
    </delete>
</mapper> 