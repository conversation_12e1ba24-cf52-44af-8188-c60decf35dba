package com.ruoyi.system.domain.datasync;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 数据库同步日志对象 sync_log
 * 
 * <AUTHOR>
 */
public class SyncLog extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 日志ID */
    private Long logId;

    /** 同步任务ID */
    @Excel(name = "同步任务ID")
    private Long syncId;

    /** 执行时间 */
    @Excel(name = "执行时间")
    private java.util.Date execTime;

    /** 是否成功（0成功 1失败） */
    @Excel(name = "是否成功", readConverterExp = "0=成功,1=失败")
    private String success;

    /** 日志消息 */
    @Excel(name = "日志消息")
    private String message;

    public void setLogId(Long logId) 
    {
        this.logId = logId;
    }

    public Long getLogId() 
    {
        return logId;
    }
    public void setSyncId(Long syncId) 
    {
        this.syncId = syncId;
    }

    public Long getSyncId() 
    {
        return syncId;
    }
    public void setExecTime(java.util.Date execTime) 
    {
        this.execTime = execTime;
    }

    public java.util.Date getExecTime() 
    {
        return execTime;
    }
    public void setSuccess(String success) 
    {
        this.success = success;
    }

    public String getSuccess() 
    {
        return success;
    }
    public void setMessage(String message) 
    {
        this.message = message;
    }

    public String getMessage() 
    {
        return message;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("logId", getLogId())
            .append("syncId", getSyncId())
            .append("execTime", getExecTime())
            .append("success", getSuccess())
            .append("message", getMessage())
            .toString();
    }
} 