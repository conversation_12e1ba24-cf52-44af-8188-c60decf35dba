version: '3'

services:
  # MySQL数据库服务
  mysql:
    image: mysql:5.7
    container_name: ruoyi-mysql
    restart: always
    environment:
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_DATABASE: ry
      TZ: Asia/Shanghai
    volumes:
      - mysql-data:/var/lib/mysql
      - ./sql:/docker-entrypoint-initdb.d/
    ports:
      - "3306:3306"
    networks:
      - ruoyi-net
    command: --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci

  # Redis缓存服务
  redis:
    image: redis:6-alpine
    container_name: ruoyi-redis
    restart: always
    volumes:
      - redis-data:/data
    networks:
      - ruoyi-net
    ports:
      - "6379:6379"

  # 后端服务
  backend:
    build:
      context: ./RuoYi-Vue
      dockerfile: ../Dockerfile.backend
    container_name: ruoyi-backend
    restart: always
    depends_on:
      - mysql
      - redis
    environment:
      # 只需要指定激活的配置文件
      - SPRING_PROFILES_ACTIVE=prod
    volumes:
      - ./RuoYi-Vue:/app/RuoYi-Vue
      - ./application-prod.yml:/app/config/application-prod.yml
    networks:
      - ruoyi-net
    ports:
      - "8080:8080"

  # 前端服务
  frontend:
    build:
      context: ./RuoYi-Vue
      dockerfile: ../Dockerfile.frontend
    container_name: ruoyi-frontend
    restart: always
    depends_on:
      - backend
    networks:
      - ruoyi-net
    ports:
      - "80:80"
    volumes:
      - ./docker/nginx.conf:/etc/nginx/conf.d/default.conf

# 网络配置
networks:
  ruoyi-net:
    driver: bridge

# 数据卷配置
volumes:
  mysql-data:
  redis-data: 