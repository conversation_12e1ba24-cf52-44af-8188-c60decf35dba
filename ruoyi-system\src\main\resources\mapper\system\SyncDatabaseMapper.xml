<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SyncDatabaseMapper">
    
    <resultMap type="com.ruoyi.system.domain.datasync.SyncDatabase" id="SyncDatabaseResult">
        <result property="dbId"    column="db_id"    />
        <result property="dbName"    column="db_name"    />
        <result property="dbType"    column="db_type"    />
        <result property="serverId"    column="server_id"    />
        <result property="serverName"    column="server_name"    />
        <result property="dbPort"    column="db_port"    />
        <result property="dbUsername"    column="db_username"    />
        <result property="dbPassword"    column="db_password"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectSyncDatabaseVo">
        select d.db_id, d.db_name, d.db_type, d.server_id, s.server_name, d.db_port, d.db_username, d.db_password, d.status, d.create_by, d.create_time, d.update_by, d.update_time, d.remark 
        from sync_database d
        left join sync_server s on d.server_id = s.server_id
    </sql>

    <select id="selectSyncDatabaseList" parameterType="com.ruoyi.system.domain.datasync.SyncDatabase" resultMap="SyncDatabaseResult">
        <include refid="selectSyncDatabaseVo"/>
        <where>  
            <if test="dbName != null  and dbName != ''"> and d.db_name like concat('%', #{dbName}, '%')</if>
            <if test="dbType != null  and dbType != ''"> and d.db_type = #{dbType}</if>
            <if test="serverId != null "> and d.server_id = #{serverId}</if>
            <if test="status != null  and status != ''"> and d.status = #{status}</if>
        </where>
    </select>
    
    <select id="selectSyncDatabaseByDbId" parameterType="Long" resultMap="SyncDatabaseResult">
        <include refid="selectSyncDatabaseVo"/>
        where d.db_id = #{dbId}
    </select>
        
    <insert id="insertSyncDatabase" parameterType="com.ruoyi.system.domain.datasync.SyncDatabase" useGeneratedKeys="true" keyProperty="dbId">
        insert into sync_database
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dbName != null">db_name,</if>
            <if test="dbType != null">db_type,</if>
            <if test="serverId != null">server_id,</if>
            <if test="dbPort != null">db_port,</if>
            <if test="dbUsername != null">db_username,</if>
            <if test="dbPassword != null">db_password,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dbName != null">#{dbName},</if>
            <if test="dbType != null">#{dbType},</if>
            <if test="serverId != null">#{serverId},</if>
            <if test="dbPort != null">#{dbPort},</if>
            <if test="dbUsername != null">#{dbUsername},</if>
            <if test="dbPassword != null">#{dbPassword},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateSyncDatabase" parameterType="com.ruoyi.system.domain.datasync.SyncDatabase">
        update sync_database
        <trim prefix="SET" suffixOverrides=",">
            <if test="dbName != null">db_name = #{dbName},</if>
            <if test="dbType != null">db_type = #{dbType},</if>
            <if test="serverId != null">server_id = #{serverId},</if>
            <if test="dbPort != null">db_port = #{dbPort},</if>
            <if test="dbUsername != null">db_username = #{dbUsername},</if>
            <if test="dbPassword != null">db_password = #{dbPassword},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where db_id = #{dbId}
    </update>

    <delete id="deleteSyncDatabaseByDbId" parameterType="Long">
        delete from sync_database where db_id = #{dbId}
    </delete>

    <delete id="deleteSyncDatabaseByDbIds" parameterType="String">
        delete from sync_database where db_id in 
        <foreach item="dbId" collection="array" open="(" separator="," close=")">
            #{dbId}
        </foreach>
    </delete>
</mapper> 