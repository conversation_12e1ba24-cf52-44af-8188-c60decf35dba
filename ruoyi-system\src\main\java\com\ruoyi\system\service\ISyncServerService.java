package com.ruoyi.system.service;

import java.util.List;
import java.util.Map;
import com.ruoyi.system.domain.datasync.SyncServer;

/**
 * 服务器Service接口
 * 
 * <AUTHOR>
 */
public interface ISyncServerService 
{
    /**
     * 查询服务器
     * 
     * @param serverId 服务器主键
     * @return 服务器
     */
    public SyncServer selectSyncServerByServerId(Long serverId);

    /**
     * 查询服务器列表
     * 
     * @param syncServer 服务器
     * @return 服务器集合
     */
    public List<SyncServer> selectSyncServerList(SyncServer syncServer);

    /**
     * 新增服务器
     * 
     * @param syncServer 服务器
     * @return 结果
     */
    public int insertSyncServer(SyncServer syncServer);

    /**
     * 修改服务器
     * 
     * @param syncServer 服务器
     * @return 结果
     */
    public int updateSyncServer(SyncServer syncServer);

    /**
     * 批量删除服务器
     * 
     * @param serverIds 需要删除的服务器主键集合
     * @return 结果
     */
    public int deleteSyncServerByServerIds(Long[] serverIds);

    /**
     * 删除服务器信息
     * 
     * @param serverId 服务器主键
     * @return 结果
     */
    public int deleteSyncServerByServerId(Long serverId);

    /**
     * 测试服务器连接
     * 
     * @param serverId 服务器主键
     * @return 结果
     */
    public boolean testServerConnection(Long serverId);

    /**
     * 获取所有可用服务器列表（下拉选项用）
     * 
     * @return 服务器选项列表
     */
    public List<SyncServer> selectServerOptions();

    /**
     * 查询服务器下的文件目录
     * 
     * @param serverId 服务器ID
     * @param dirPath 目录路径，为空时查询根目录
     * @return 文件目录列表
     */
    public List<Map<String, Object>> listServerFiles(Long serverId, String dirPath);

    /**
     * 在服务器上执行命令
     * 
     * @param serverId 服务器ID
     * @param command 要执行的命令
     * @return 命令执行结果
     */
    public Map<String, Object> executeCommand(Long serverId, String command);

    /**
     * 获取服务器上的进程列表
     * 
     * @param serverId 服务器ID
     * @return 进程列表
     */
    public List<Map<String, Object>> getServerProcesses(Long serverId);

    /**
     * 终止服务器上的进程
     * 
     * @param serverId 服务器ID
     * @param pid 进程ID
     * @return 操作结果
     */
    public boolean killServerProcess(Long serverId, String pid);

    /**
     * 获取Docker信息
     * 
     * @param serverId 服务器ID
     * @return Docker信息
     */
    public Map<String, Object> getDockerInfo(Long serverId);
    
    /**
     * 获取Docker容器列表
     * 
     * @param serverId 服务器ID
     * @return 容器列表
     */
    public List<Map<String, Object>> getDockerContainers(Long serverId);
    
    /**
     * 获取Docker镜像列表
     * 
     * @param serverId 服务器ID
     * @return 镜像列表
     */
    public List<Map<String, Object>> getDockerImages(Long serverId);
    
    /**
     * 获取Docker Compose配置
     * 
     * @param serverId 服务器ID
     * @param dirPath 目录路径
     * @return Compose配置文件列表
     */
    public List<Map<String, Object>> getDockerComposeFiles(Long serverId, String dirPath);
    
    /**
     * 读取Docker Compose配置文件内容
     * 
     * @param serverId 服务器ID
     * @param filePath 文件路径
     * @return 文件内容
     */
    public String readDockerComposeFile(Long serverId, String filePath);
    
    /**
     * 迁移Docker容器到目标服务器
     * 
     * @param sourceServerId 源服务器ID
     * @param targetServerId 目标服务器ID
     * @param containerId 容器ID，为空时迁移所有容器
     * @return 迁移结果
     */
    public Map<String, Object> migrateDockerContainer(Long sourceServerId, Long targetServerId, String containerId);
    
    /**
     * 迁移Docker Compose配置到目标服务器
     * 
     * @param sourceServerId 源服务器ID
     * @param targetServerId 目标服务器ID
     * @param composePath Compose配置目录
     * @return 迁移结果
     */
    public Map<String, Object> migrateDockerCompose(Long sourceServerId, Long targetServerId, String composePath);

    /**
     * 获取服务器上可用的软件包列表
     * 
     * @param serverId 服务器ID
     * @param keyword 搜索关键词
     * @return 软件包列表
     */
    public List<Map<String, Object>> getAvailableSoftwarePackages(Long serverId, String keyword);
    
    /**
     * 获取服务器上已安装的软件包列表
     * 
     * @param serverId 服务器ID
     * @param keyword 搜索关键词
     * @return 软件包列表
     */
    public List<Map<String, Object>> getInstalledSoftwarePackages(Long serverId, String keyword);
    
    /**
     * 安装软件包
     * 
     * @param serverId 服务器ID
     * @param packageName 软件包名称
     * @return 安装结果
     */
    public Map<String, Object> installSoftwarePackage(Long serverId, String packageName);
    
    /**
     * 卸载软件包
     * 
     * @param serverId 服务器ID
     * @param packageName 软件包名称
     * @return 卸载结果
     */
    public Map<String, Object> uninstallSoftwarePackage(Long serverId, String packageName);
    
    /**
     * 更新软件包
     * 
     * @param serverId 服务器ID
     * @param packageName 软件包名称，为空时更新所有软件包
     * @return 更新结果
     */
    public Map<String, Object> updateSoftwarePackage(Long serverId, String packageName);

    /**
     * 一键迁移全部Docker容器
     * 
     * @param sourceServerId 源服务器ID
     * @param targetServerId 目标服务器ID
     * @return 迁移结果
     */
    public Map<String, Object> migrateAllDockerContainers(Long sourceServerId, Long targetServerId);
    
    /**
     * 迁移Docker镜像到目标服务器
     * 
     * @param sourceServerId 源服务器ID
     * @param targetServerId 目标服务器ID
     * @param imageId 镜像ID，为空时迁移所有镜像
     * @return 迁移结果
     */
    public Map<String, Object> migrateDockerImage(Long sourceServerId, Long targetServerId, String imageId);

    /**
     * 设置两个服务器之间的SSH免密登录
     *
     * @param sourceServerId 源服务器ID
     * @param targetServerId 目标服务器ID
     * @return 设置结果
     */
    public Map<String, Object> setupSSHKeyAuth(Long sourceServerId, Long targetServerId);

    /**
     * 启动Docker容器
     *
     * @param serverId 服务器ID
     * @param containerId 容器ID
     * @return 操作结果
     */
    public Map<String, Object> startDockerContainer(Long serverId, String containerId);
    
    /**
     * 停止Docker容器
     *
     * @param serverId 服务器ID
     * @param containerId 容器ID
     * @return 操作结果
     */
    public Map<String, Object> stopDockerContainer(Long serverId, String containerId);
    
    /**
     * 删除Docker容器
     *
     * @param serverId 服务器ID
     * @param containerId 容器ID
     * @return 操作结果
     */
    public Map<String, Object> removeDockerContainer(Long serverId, String containerId);
    
    /**
     * 获取Docker容器日志
     * 
     * @param serverId 服务器ID
     * @param containerId 容器ID
     * @param lines 获取的日志行数，可选
     * @return 容器日志
     */
    public Map<String, Object> getDockerContainerLogs(Long serverId, String containerId, Integer lines);
    
    /**
     * 安装Docker Compose
     *
     * @param serverId 服务器ID
     * @return 安装结果
     */
    public Map<String, Object> installDockerCompose(Long serverId);
    
    /**
     * 安装Docker Compose（指定版本）
     *
     * @param serverId 服务器ID
     * @param version Docker Compose版本，为空时使用默认版本
     * @return 安装结果
     */
    public Map<String, Object> installDockerCompose(Long serverId, String version);

    /**
     * 安装Docker Compose（指定版本和安装方式）
     *
     * @param serverId 服务器ID
     * @param version Docker Compose版本，为空时使用默认版本
     * @param installMethod 安装方式，"user"为当前用户安装，"system"为所有用户安装
     * @return 安装结果
     */
    public Map<String, Object> installDockerCompose(Long serverId, String version, String installMethod);

    /**
     * 使用Docker Compose部署容器
     * 
     * @param serverId 服务器ID
     * @param composeFilePath Compose文件路径
     * @param operation 操作类型(up/down)
     * @return 结果
     */
    public Map<String, Object> runDockerCompose(Long serverId, String composeFilePath, String operation);

    /**
     * 将容器导出为Docker Compose配置
     *
     * @param serverId 服务器ID
     * @param containerId 容器ID（如果指定allContainers为true，则可以为null）
     * @param outputPath 输出路径
     * @param allContainers 是否导出所有容器
     * @return 生成结果
     */
    public Map<String, Object> generateDockerCompose(Long serverId, String containerId, String outputPath, Boolean allContainers);

    /**
     * 将文件从一个服务器同步到另一个服务器
     *
     * @param sourceServerId 源服务器ID
     * @param targetServerId 目标服务器ID
     * @param sourcePath 源文件路径
     * @param targetPath 目标文件路径
     * @return 同步结果
     */
    public Map<String, Object> syncFileBetweenServers(Long sourceServerId, Long targetServerId, String sourcePath, String targetPath);

    /**
     * 在服务器上执行命令（带超时控制）
     * 
     * @param serverId 服务器ID
     * @param command 要执行的命令
     * @param timeoutSeconds 超时时间（秒）
     * @return 命令执行结果
     */
    public Map<String, Object> executeCommandWithTimeout(Long serverId, String command, int timeoutSeconds);
} 