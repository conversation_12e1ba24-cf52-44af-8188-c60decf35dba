-- RabbitMQ模板（简化版）
INSERT INTO sys_docker_template (
    template_name, template_description, image_name, image_tag, 
    port_mappings, volume_mappings, environment_vars, network_mode, 
    restart_policy, command, is_system, use_compose, 
    create_by, create_time, remark
) VALUES (
    'RabbitMQ消息队列(简化版)', 
    'RabbitMQ是实现了高级消息队列协议（AMQP）的开源消息代理软件，带管理界面',
    'rabbitmq', 
    'management', 
    '5672:5672,15672:15672', 
    '/data/rabbitmq/data:/var/lib/rabbitmq', 
    'RABBITMQ_DEFAULT_USER=admin,RABBITMQ_DEFAULT_PASS=admin123', 
    'bridge', 
    'always', 
    NULL, 
    '1', 
    '0', 
    'admin', 
    sysdate(), 
    'RabbitMQ服务，默认端口5672，管理界面15672，用户名/密码：admin/admin123'
);

-- 部署说明
/*
简化版RabbitMQ模板说明：

1. 此模板已经移除了所有可能导致问题的配置，包括：
   - 复杂的主机名设置
   - 自定义启动命令
   - 额外的插件配置
   - 多余的卷映射

2. 部署前准备：
   - 确保目录存在：mkdir -p /data/rabbitmq/data
   - 确保目录权限正确：chmod -R 777 /data/rabbitmq

3. 部署后访问管理界面：
   - 地址：http://服务器IP:15672
   - 用户名：admin
   - 密码：admin123

4. 如果需要启用其他插件，可在容器启动后手动启用：
   docker exec -it rabbitmq rabbitmq-plugins enable rabbitmq_mqtt rabbitmq_federation rabbitmq_stomp
*/ 