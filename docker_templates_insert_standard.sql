-- Docker模板插入脚本（常规方式）
-- 包含常用中间件：Nacos、RabbitMQ、RocketMQ、MongoDB等

-- Nacos模板
INSERT INTO sys_docker_template (
    template_name, template_description, image_name, image_tag, 
    port_mappings, volume_mappings, environment_vars, network_mode, 
    restart_policy, command, is_system, use_compose, 
    create_by, create_time, remark
) VALUES (
    'Nacos服务注册中心', 
    'Nacos是阿里巴巴开源的一个更易于构建云原生应用的动态服务发现、配置管理和服务管理平台',
    'nacos/nacos-server', 
    'latest', 
    '8848:8848,9848:9848,9849:9849', 
    '/home/<USER>/logs:/home/<USER>/logs,/home/<USER>/conf:/home/<USER>/conf', 
    'MODE=standalone,JVM_XMS=512m,JVM_XMX=512m', 
    'bridge', 
    'always', 
    NULL, 
    '1', 
    '0', 
    'admin', 
    sysdate(), 
    '单机模式Nacos服务，默认端口8848，用户名/密码：nacos/nacos'
);

-- RabbitMQ模板
INSERT INTO sys_docker_template (
    template_name, template_description, image_name, image_tag, 
    port_mappings, volume_mappings, environment_vars, network_mode, 
    restart_policy, command, is_system, use_compose, 
    create_by, create_time, remark
) VALUES (
    'RabbitMQ消息队列', 
    'RabbitMQ是实现了高级消息队列协议（AMQP）的开源消息代理软件',
    'rabbitmq', 
    'management', 
    '5672:5672,15672:15672', 
    '/data/rabbitmq/data:/var/lib/rabbitmq,/data/rabbitmq/log:/var/log/rabbitmq,/data/rabbitmq/plugins:/etc/rabbitmq/enabled_plugins', 
    'RABBITMQ_DEFAULT_USER=admin,RABBITMQ_DEFAULT_PASS=admin123', 
    'bridge', 
    'always', 
    NULL, 
    '1', 
    '0', 
    'admin', 
    sysdate(), 
    'RabbitMQ服务，默认端口5672，管理界面15672，用户名/密码：admin/admin123'
);

-- Redis模板
INSERT INTO sys_docker_template (
    template_name, template_description, image_name, image_tag, 
    port_mappings, volume_mappings, environment_vars, network_mode, 
    restart_policy, command, is_system, use_compose, 
    create_by, create_time, remark
) VALUES (
    'Redis缓存服务', 
    'Redis是一个开源的内存数据库，常用作缓存、消息队列和分布式锁',
    'redis', 
    'latest', 
    '6379:6379', 
    '/data/redis:/data', 
    NULL, 
    'bridge', 
    'always', 
    'redis-server --requirepass "redis123" --appendonly yes', 
    '1', 
    '0', 
    'admin', 
    sysdate(), 
    'Redis缓存服务，默认端口6379，密码redis123'
);

-- MySQL模板
INSERT INTO sys_docker_template (
    template_name, template_description, image_name, image_tag, 
    port_mappings, volume_mappings, environment_vars, network_mode, 
    restart_policy, command, is_system, use_compose, 
    create_by, create_time, remark
) VALUES (
    'MySQL数据库', 
    'MySQL是世界上最流行的开源关系型数据库管理系统',
    'mysql', 
    '8.0', 
    '3306:3306', 
    '/data/mysql/data:/var/lib/mysql,/data/mysql/conf:/etc/mysql/conf.d,/data/mysql/init:/docker-entrypoint-initdb.d', 
    'MYSQL_ROOT_PASSWORD=mysql123,MYSQL_DATABASE=testdb,TZ=Asia/Shanghai', 
    'bridge', 
    'always', 
    '--character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci', 
    '1', 
    '0', 
    'admin', 
    sysdate(), 
    'MySQL数据库8.0版本，默认端口3306，root密码mysql123'
);

-- MongoDB模板
INSERT INTO sys_docker_template (
    template_name, template_description, image_name, image_tag, 
    port_mappings, volume_mappings, environment_vars, network_mode, 
    restart_policy, command, is_system, use_compose, 
    create_by, create_time, remark
) VALUES (
    'MongoDB数据库', 
    'MongoDB是一个基于分布式文件存储的开源NoSQL数据库系统',
    'mongo', 
    'latest', 
    '27017:27017', 
    '/data/mongodb/data:/data/db,/data/mongodb/config:/data/configdb', 
    'MONGO_INITDB_ROOT_USERNAME=admin,MONGO_INITDB_ROOT_PASSWORD=admin123', 
    'bridge', 
    'always', 
    NULL, 
    '1', 
    '0', 
    'admin', 
    sysdate(), 
    'MongoDB数据库，默认端口27017，用户名/密码：admin/admin123'
);

-- Elasticsearch模板
INSERT INTO sys_docker_template (
    template_name, template_description, image_name, image_tag, 
    port_mappings, volume_mappings, environment_vars, network_mode, 
    restart_policy, command, is_system, use_compose, 
    create_by, create_time, remark
) VALUES (
    'Elasticsearch搜索引擎', 
    'Elasticsearch是一个分布式、RESTful风格的搜索和数据分析引擎',
    'elasticsearch', 
    '7.17.9', 
    '9200:9200,9300:9300', 
    '/data/elasticsearch/data:/usr/share/elasticsearch/data', 
    'discovery.type=single-node,ES_JAVA_OPTS=-Xms512m -Xmx512m,xpack.security.enabled=false', 
    'bridge', 
    'always', 
    NULL, 
    '1', 
    '0', 
    'admin', 
    sysdate(), 
    'Elasticsearch 7.17.9，默认端口9200'
);

-- RocketMQ Name Server模板
INSERT INTO sys_docker_template (
    template_name, template_description, image_name, image_tag, 
    port_mappings, volume_mappings, environment_vars, network_mode, 
    restart_policy, command, is_system, use_compose, 
    create_by, create_time, remark
) VALUES (
    'RocketMQ Name Server', 
    'RocketMQ Name Server，用于服务发现和路由',
    'apache/rocketmq', 
    '4.9.4', 
    '9876:9876', 
    '/data/rocketmq/namesrv/logs:/home/<USER>/logs,/data/rocketmq/namesrv/store:/home/<USER>/store', 
    NULL, 
    'bridge', 
    'always', 
    'sh mqnamesrv', 
    '1', 
    '0', 
    'admin', 
    sysdate(), 
    'RocketMQ Name Server，默认端口9876，需要先启动此容器再启动Broker'
);

-- RocketMQ Broker模板
INSERT INTO sys_docker_template (
    template_name, template_description, image_name, image_tag, 
    port_mappings, volume_mappings, environment_vars, network_mode, 
    restart_policy, command, is_system, use_compose, 
    create_by, create_time, remark
) VALUES (
    'RocketMQ Broker', 
    'RocketMQ Broker，负责消息存储和转发',
    'apache/rocketmq', 
    '4.9.4', 
    '10909:10909,10911:10911', 
    '/data/rocketmq/broker/logs:/home/<USER>/logs,/data/rocketmq/broker/store:/home/<USER>/store,/data/rocketmq/broker/conf/broker.conf:/opt/rocketmq/conf/broker.conf', 
    'JAVA_OPT_EXT=-server -Xms512m -Xmx512m', 
    'bridge', 
    'always', 
    'sh mqbroker -n namesrv:9876', 
    '1', 
    '0', 
    'admin', 
    sysdate(), 
    'RocketMQ Broker，需要先创建broker.conf配置文件，并确保Name Server已启动'
);

-- RocketMQ Dashboard模板
INSERT INTO sys_docker_template (
    template_name, template_description, image_name, image_tag, 
    port_mappings, volume_mappings, environment_vars, network_mode, 
    restart_policy, command, is_system, use_compose, 
    create_by, create_time, remark
) VALUES (
    'RocketMQ Dashboard', 
    'RocketMQ管理控制台，用于监控和管理RocketMQ集群',
    'apacherocketmq/rocketmq-dashboard', 
    'latest', 
    '8080:8080', 
    NULL, 
    'JAVA_OPTS=-Drocketmq.namesrv.addr=namesrv:9876', 
    'bridge', 
    'always', 
    NULL, 
    '1', 
    '0', 
    'admin', 
    sysdate(), 
    'RocketMQ管理控制台，默认端口8080，需要确保Name Server已启动'
);

-- Nginx模板
INSERT INTO sys_docker_template (
    template_name, template_description, image_name, image_tag, 
    port_mappings, volume_mappings, environment_vars, network_mode, 
    restart_policy, command, is_system, use_compose, 
    create_by, create_time, remark
) VALUES (
    'Nginx Web服务器', 
    'Nginx是一个高性能的HTTP和反向代理Web服务器',
    'nginx', 
    'latest', 
    '80:80,443:443', 
    '/data/nginx/html:/usr/share/nginx/html,/data/nginx/conf/nginx.conf:/etc/nginx/nginx.conf,/data/nginx/conf.d:/etc/nginx/conf.d,/data/nginx/logs:/var/log/nginx', 
    NULL, 
    'bridge', 
    'always', 
    NULL, 
    '1', 
    '0', 
    'admin', 
    sysdate(), 
    'Nginx Web服务器，默认端口80和443，需要提前准备配置文件'
);

-- Zookeeper模板
INSERT INTO sys_docker_template (
    template_name, template_description, image_name, image_tag, 
    port_mappings, volume_mappings, environment_vars, network_mode, 
    restart_policy, command, is_system, use_compose, 
    create_by, create_time, remark
) VALUES (
    'Zookeeper服务', 
    'Zookeeper是一个分布式协调服务，为分布式应用提供一致性服务',
    'zookeeper', 
    'latest', 
    '2181:2181', 
    '/data/zookeeper/data:/data,/data/zookeeper/datalog:/datalog,/data/zookeeper/logs:/logs', 
    'ZOO_MY_ID=1,ZOO_SERVERS=server.1=localhost:2888:3888;2181', 
    'bridge', 
    'always', 
    NULL, 
    '1', 
    '0', 
    'admin', 
    sysdate(), 
    'Zookeeper服务，默认端口2181，单机模式'
);

-- Kafka模板
INSERT INTO sys_docker_template (
    template_name, template_description, image_name, image_tag, 
    port_mappings, volume_mappings, environment_vars, network_mode, 
    restart_policy, command, is_system, use_compose, 
    create_by, create_time, remark
) VALUES (
    'Kafka消息队列', 
    'Kafka是一个分布式流处理平台，用于构建实时数据流水线和流应用程序',
    'wurstmeister/kafka', 
    'latest', 
    '9092:9092', 
    '/data/kafka/data:/kafka', 
    'KAFKA_ADVERTISED_HOST_NAME=localhost,KAFKA_ZOOKEEPER_CONNECT=zookeeper:2181,KAFKA_ADVERTISED_PORT=9092', 
    'bridge', 
    'always', 
    NULL, 
    '1', 
    '0', 
    'admin', 
    sysdate(), 
    'Kafka消息队列，默认端口9092，需要先启动Zookeeper'
);

-- Redis Commander模板
INSERT INTO sys_docker_template (
    template_name, template_description, image_name, image_tag, 
    port_mappings, volume_mappings, environment_vars, network_mode, 
    restart_policy, command, is_system, use_compose, 
    create_by, create_time, remark
) VALUES (
    'Redis Commander', 
    'Redis的Web管理界面，用于可视化管理Redis数据库',
    'rediscommander/redis-commander', 
    'latest', 
    '8081:8081', 
    NULL, 
    'REDIS_HOSTS=local:redis:6379:0:redis123', 
    'bridge', 
    'always', 
    NULL, 
    '1', 
    '0', 
    'admin', 
    sysdate(), 
    'Redis Commander管理界面，默认端口8081，需要先启动Redis服务'
);

-- phpMyAdmin模板
INSERT INTO sys_docker_template (
    template_name, template_description, image_name, image_tag, 
    port_mappings, volume_mappings, environment_vars, network_mode, 
    restart_policy, command, is_system, use_compose, 
    create_by, create_time, remark
) VALUES (
    'phpMyAdmin', 
    'MySQL的Web管理界面，用于可视化管理MySQL数据库',
    'phpmyadmin/phpmyadmin', 
    'latest', 
    '8080:80', 
    NULL, 
    'PMA_HOST=mysql,PMA_PORT=3306,MYSQL_ROOT_PASSWORD=mysql123', 
    'bridge', 
    'always', 
    NULL, 
    '1', 
    '0', 
    'admin', 
    sysdate(), 
    'phpMyAdmin管理界面，默认端口8080，需要先启动MySQL服务'
);

-- Kibana模板
INSERT INTO sys_docker_template (
    template_name, template_description, image_name, image_tag, 
    port_mappings, volume_mappings, environment_vars, network_mode, 
    restart_policy, command, is_system, use_compose, 
    create_by, create_time, remark
) VALUES (
    'Kibana', 
    'Elasticsearch的数据可视化和用户界面',
    'kibana', 
    '7.17.9', 
    '5601:5601', 
    NULL, 
    'ELASTICSEARCH_HOSTS=http://elasticsearch:9200', 
    'bridge', 
    'always', 
    NULL, 
    '1', 
    '0', 
    'admin', 
    sysdate(), 
    'Kibana 7.17.9，默认端口5601，需要先启动Elasticsearch'
);

-- Mongo Express模板
INSERT INTO sys_docker_template (
    template_name, template_description, image_name, image_tag, 
    port_mappings, volume_mappings, environment_vars, network_mode, 
    restart_policy, command, is_system, use_compose, 
    create_by, create_time, remark
) VALUES (
    'Mongo Express', 
    'MongoDB的Web管理界面，用于可视化管理MongoDB数据库',
    'mongo-express', 
    'latest', 
    '8081:8081', 
    NULL, 
    'ME_CONFIG_MONGODB_ADMINUSERNAME=admin,ME_CONFIG_MONGODB_ADMINPASSWORD=admin123,ME_CONFIG_MONGODB_SERVER=mongodb', 
    'bridge', 
    'always', 
    NULL, 
    '1', 
    '0', 
    'admin', 
    sysdate(), 
    'Mongo Express管理界面，默认端口8081，需要先启动MongoDB服务'
); 