<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="数据库名称" prop="dbName">
        <el-input
          v-model="queryParams.dbName"
          placeholder="请输入数据库名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="数据库类型" prop="dbType">
        <el-select v-model="queryParams.dbType" placeholder="请选择数据库类型" clearable>
          <el-option
            v-for="dict in dict.type.sys_db_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="所属服务器" prop="serverId">
        <el-select v-model="queryParams.serverId" placeholder="请选择所属服务器" clearable>
          <el-option
            v-for="server in serverOptions"
            :key="server.serverId"
            :label="server.serverName"
            :value="server.serverId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="请选择状态" clearable>
          <el-option
            v-for="dict in dict.type.sys_normal_disable"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:database:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:database:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:database:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:database:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="databaseList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="数据库ID" align="center" prop="dbId" v-if="false" />
      <el-table-column label="数据库名称" align="center" prop="dbName" />
      <el-table-column label="数据库类型" align="center" prop="dbType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_db_type" :value="scope.row.dbType"/>
        </template>
      </el-table-column>
      <el-table-column label="所属服务器" align="center" prop="serverName" />
      <el-table-column label="端口" align="center" prop="dbPort" width="80" />
      <el-table-column label="用户名" align="center" prop="dbUsername" />
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:database:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:database:remove']"
          >删除</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-link"
            @click="handleTestConnection(scope.row)"
            v-hasPermi="['system:database:test']"
          >测试连接</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleViewDetail(scope.row)"
            v-hasPermi="['system:database:query']"
          >查看</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改数据库对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="700px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="数据库名称" prop="dbName">
              <el-input v-model="form.dbName" placeholder="请输入数据库名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="数据库类型" prop="dbType">
              <el-select v-model="form.dbType" placeholder="请选择数据库类型">
                <el-option
                  v-for="dict in dict.type.sys_db_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="所属服务器" prop="serverId">
              <el-select v-model="form.serverId" placeholder="请选择所属服务器" @change="handleServerChange">
                <el-option
                  v-for="server in serverOptions"
                  :key="server.serverId"
                  :label="server.serverName"
                  :value="server.serverId"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="端口" prop="dbPort">
              <el-input v-model="form.dbPort" placeholder="请输入端口号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="用户名" prop="dbUsername">
              <el-input v-model="form.dbUsername" placeholder="请输入用户名" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="密码" prop="dbPassword">
              <el-input v-model="form.dbPassword" placeholder="请输入密码" type="password" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-radio-group v-model="form.status">
                <el-radio
                  v-for="dict in dict.type.sys_normal_disable"
                  :key="dict.value"
                  :label="dict.value"
                >{{dict.label}}</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listDatabase, getDatabase, delDatabase, addDatabase, updateDatabase, testDatabaseConnection } from "@/api/datasync/database";
import { listServerOptions } from "@/api/datasync/server";

export default {
  name: "Database",
  dicts: ['sys_normal_disable', 'sys_db_type'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 数据库表格数据
      databaseList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 服务器选项
      serverOptions: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        dbName: null,
        dbType: null,
        serverId: null,
        status: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        dbName: [
          { required: true, message: "数据库名称不能为空", trigger: "blur" }
        ],
        dbType: [
          { required: true, message: "数据库类型不能为空", trigger: "change" }
        ],
        serverId: [
          { required: true, message: "所属服务器不能为空", trigger: "change" }
        ],
        dbPort: [
          { required: true, message: "端口不能为空", trigger: "blur" }
        ],
        dbUsername: [
          { required: true, message: "用户名不能为空", trigger: "blur" }
        ],
        dbPassword: [
          { required: true, message: "密码不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.getServerOptions();
  },
  methods: {
    /** 查询数据库列表 */
    getList() {
      this.loading = true;
      listDatabase(this.queryParams).then(response => {
        this.databaseList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 获取服务器选项 */
    getServerOptions() {
      listServerOptions().then(response => {
        this.serverOptions = response.data;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        dbId: null,
        dbName: null,
        dbType: null,
        serverId: null,
        serverName: null,
        dbPort: null,
        dbUsername: null,
        dbPassword: null,
        status: "0",
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.dbId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加数据库";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const dbId = row.dbId || this.ids[0];
      getDatabase(dbId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改数据库";
      });
    },
    /** 服务器选择变更 */
    handleServerChange(serverId) {
      const server = this.serverOptions.find(item => item.serverId === serverId);
      if (server) {
        this.form.serverName = server.serverName;
      }
    },
    /** 测试数据库连接 */
    handleTestConnection(row) {
      const dbId = row.dbId;
      this.$modal.loading("正在测试连接中，请稍候...");
      testDatabaseConnection(dbId).then(response => {
        this.$modal.closeLoading();
        this.$modal.msgSuccess("连接测试成功");
      }).catch(() => {
        this.$modal.closeLoading();
      });
    },
    /** 查看数据库详情 */
    handleViewDetail(row) {
      const dbId = row.dbId;
      this.$router.push({ 
        path: '/datasync/database/detail', 
        query: { dbId: dbId, name: row.dbName } 
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.dbId != null) {
            updateDatabase(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDatabase(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const dbIds = row.dbId || this.ids;
      this.$modal.confirm('是否确认删除数据库编号为"' + dbIds + '"的数据项？').then(function() {
        return delDatabase(dbIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/sync/database/export', {
        ...this.queryParams
      }, `database_${new Date().getTime()}.xlsx`);
    }
  }
};
</script> 