import request from '@/utils/request'

// 查询数据库类型数据列表
export function listDbTypeData(query) {
  return request({
    url: '/system/dict/data/list',
    method: 'get',
    params: {
      ...query,
      dictType: 'sys_db_type'
    }
  })
}

// 查询数据库类型数据详细
export function getDbType(dictCode) {
  return request({
    url: '/system/dict/data/' + dictCode,
    method: 'get'
  })
}

// 新增数据库类型数据
export function addDbType(data) {
  // 确保字典类型为sys_db_type
  data.dictType = 'sys_db_type'
  return request({
    url: '/system/dict/data',
    method: 'post',
    data: data
  })
}

// 修改数据库类型数据
export function updateDbType(data) {
  return request({
    url: '/system/dict/data',
    method: 'put',
    data: data
  })
}

// 删除数据库类型数据
export function delDbType(dictCode) {
  return request({
    url: '/system/dict/data/' + dictCode,
    method: 'delete'
  })
}

// 导出数据库类型数据
export function exportDbType(query) {
  return request({
    url: '/system/dict/data/export',
    method: 'get',
    params: {
      ...query,
      dictType: 'sys_db_type'
    }
  })
} 