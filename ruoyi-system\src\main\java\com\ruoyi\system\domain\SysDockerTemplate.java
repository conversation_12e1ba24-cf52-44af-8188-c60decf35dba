package com.ruoyi.system.domain;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * Docker容器模板对象 sys_docker_template
 * 
 * <AUTHOR>
 */
public class SysDockerTemplate extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 模板ID */
    private Long templateId;

    /** 模板名称 */
    @Excel(name = "模板名称")
    private String templateName;

    /** 模板描述 */
    @Excel(name = "模板描述")
    private String templateDescription;

    /** 镜像名称 */
    @Excel(name = "镜像名称")
    private String imageName;

    /** 镜像标签 */
    @Excel(name = "镜像标签")
    private String imageTag;

    /** 端口映射(格式: 宿主机端口:容器端口,分隔) */
    @Excel(name = "端口映射")
    private String portMappings;

    /** 卷映射(格式: 宿主机路径:容器路径,分隔) */
    @Excel(name = "卷映射")
    private String volumeMappings;

    /** 环境变量(格式: 变量名=变量值,分隔) */
    @Excel(name = "环境变量")
    private String environmentVars;

    /** 网络模式 */
    @Excel(name = "网络模式")
    private String networkMode;

    /** 重启策略 */
    @Excel(name = "重启策略")
    private String restartPolicy;

    /** 启动命令 */
    private String command;

    /** 是否系统模板(0否 1是) */
    @Excel(name = "是否系统模板", readConverterExp = "0=否,1=是")
    private String isSystem;

    /** 是否使用Docker Compose(0否 1是) */
    @Excel(name = "是否使用Compose", readConverterExp = "0=否,1=是")
    private String useCompose;

    /** Docker Compose配置 */
    private String composeConfig;

    public void setTemplateId(Long templateId) 
    {
        this.templateId = templateId;
    }

    public Long getTemplateId() 
    {
        return templateId;
    }

    public void setTemplateName(String templateName) 
    {
        this.templateName = templateName;
    }

    public String getTemplateName() 
    {
        return templateName;
    }

    public void setTemplateDescription(String templateDescription) 
    {
        this.templateDescription = templateDescription;
    }

    public String getTemplateDescription() 
    {
        return templateDescription;
    }

    public void setImageName(String imageName) 
    {
        this.imageName = imageName;
    }

    public String getImageName() 
    {
        return imageName;
    }

    public void setImageTag(String imageTag) 
    {
        this.imageTag = imageTag;
    }

    public String getImageTag() 
    {
        return imageTag;
    }

    public void setPortMappings(String portMappings) 
    {
        this.portMappings = portMappings;
    }

    public String getPortMappings() 
    {
        return portMappings;
    }

    public void setVolumeMappings(String volumeMappings) 
    {
        this.volumeMappings = volumeMappings;
    }

    public String getVolumeMappings() 
    {
        return volumeMappings;
    }

    public void setEnvironmentVars(String environmentVars) 
    {
        this.environmentVars = environmentVars;
    }

    public String getEnvironmentVars() 
    {
        return environmentVars;
    }

    public void setNetworkMode(String networkMode) 
    {
        this.networkMode = networkMode;
    }

    public String getNetworkMode() 
    {
        return networkMode;
    }

    public void setRestartPolicy(String restartPolicy) 
    {
        this.restartPolicy = restartPolicy;
    }

    public String getRestartPolicy() 
    {
        return restartPolicy;
    }

    public void setCommand(String command) 
    {
        this.command = command;
    }

    public String getCommand() 
    {
        return command;
    }

    public void setIsSystem(String isSystem) 
    {
        this.isSystem = isSystem;
    }

    public String getIsSystem() 
    {
        return isSystem;
    }

    public void setUseCompose(String useCompose)
    {
        this.useCompose = useCompose;
    }
    
    public String getUseCompose()
    {
        return useCompose;
    }
    
    public void setComposeConfig(String composeConfig)
    {
        this.composeConfig = composeConfig;
    }
    
    public String getComposeConfig()
    {
        return composeConfig;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("templateId", getTemplateId())
            .append("templateName", getTemplateName())
            .append("templateDescription", getTemplateDescription())
            .append("imageName", getImageName())
            .append("imageTag", getImageTag())
            .append("portMappings", getPortMappings())
            .append("volumeMappings", getVolumeMappings())
            .append("environmentVars", getEnvironmentVars())
            .append("networkMode", getNetworkMode())
            .append("restartPolicy", getRestartPolicy())
            .append("command", getCommand())
            .append("isSystem", getIsSystem())
            .append("useCompose", getUseCompose())
            .append("composeConfig", getComposeConfig())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
} 