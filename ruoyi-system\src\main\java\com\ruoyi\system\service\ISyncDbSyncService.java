package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.datasync.SyncDbSync;

/**
 * 数据库同步Service接口
 * 
 * <AUTHOR>
 */
public interface ISyncDbSyncService 
{
    /**
     * 查询数据库同步
     * 
     * @param syncId 数据库同步主键
     * @return 数据库同步
     */
    public SyncDbSync selectSyncDbSyncBySyncId(Long syncId);

    /**
     * 查询数据库同步列表
     * 
     * @param syncDbSync 数据库同步
     * @return 数据库同步集合
     */
    public List<SyncDbSync> selectSyncDbSyncList(SyncDbSync syncDbSync);

    /**
     * 新增数据库同步
     * 
     * @param syncDbSync 数据库同步
     * @return 结果
     */
    public int insertSyncDbSync(SyncDbSync syncDbSync);

    /**
     * 修改数据库同步
     * 
     * @param syncDbSync 数据库同步
     * @return 结果
     */
    public int updateSyncDbSync(SyncDbSync syncDbSync);

    /**
     * 批量删除数据库同步
     * 
     * @param syncIds 需要删除的数据库同步主键集合
     * @return 结果
     */
    public int deleteSyncDbSyncBySyncIds(Long[] syncIds);

    /**
     * 删除数据库同步信息
     * 
     * @param syncId 数据库同步主键
     * @return 结果
     */
    public int deleteSyncDbSyncBySyncId(Long syncId);

    /**
     * 执行数据库同步
     * 
     * @param syncId 数据库同步主键
     * @return 结果
     */
    public boolean executeSyncTask(Long syncId);
} 