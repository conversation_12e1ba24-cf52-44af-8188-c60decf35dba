# 数据库同步功能说明

## 功能修改说明

根据需求，我们对数据库同步功能进行了以下修改：

1. **区分同步类型和同步方式概念**：
   - **同步类型**：指触发方式，分为"手动同步"和"自动同步"
   - **同步方式**：指数据处理方式，分为"全量同步"和"增量同步"

2. **数据库结构修改**：
   - 添加 `sync_mode` 字段到 `sync_db_sync` 表，用于存储同步方式
   - 修改 `sync_type` 字段的注释，表示同步类型（手动/自动）

3. **前端界面修改**：
   - 在表单中添加了"同步方式"选择，用户可以选择全量同步或增量同步
   - 全量同步选项添加了警告提示，提醒用户将清空目标表数据
   - 表格中显示同步类型和同步方式两个独立的列
   - 执行同步任务时，提示用户正在执行的同步类型和方式

4. **后端逻辑修改**：
   - 修改了 `SyncDbSync` 实体类，添加了 `syncMode` 字段
   - 修改了执行同步任务的逻辑，根据 `syncMode` 字段而不是 `syncType` 字段来决定是全量同步还是增量同步

## 升级步骤

1. 执行数据库脚本：
   ```sql
   -- 修改sync_db_sync表，添加sync_mode字段
   ALTER TABLE sync_db_sync ADD COLUMN sync_mode char(1) DEFAULT '1' COMMENT '同步方式（1全量同步 2增量同步）';
   
   -- 修改同步类型字段的注释
   ALTER TABLE sync_db_sync MODIFY COLUMN sync_type char(1) COMMENT '同步类型（0手动同步 1自动同步）';
   
   -- 更新所有现有数据，设置默认同步方式为全量同步
   UPDATE sync_db_sync SET sync_mode = '1' WHERE sync_mode IS NULL;
   
   -- 更新所有现有数据，修改同步类型的值
   UPDATE sync_db_sync SET sync_type = '0' WHERE sync_type = '1'; -- 将全量同步改为手动同步
   UPDATE sync_db_sync SET sync_type = '1' WHERE sync_type = '2'; -- 将增量同步改为自动同步
   ```

2. 执行字典数据脚本：
   ```sql
   -- 同步方式字典
   insert into sys_dict_type(dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)
   values(101, '同步方式', 'sys_sync_mode', '0', 'admin', sysdate(), '', null, '同步方式列表');
   
   -- 同步方式字典数据
   insert into sys_dict_data(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
   values(1101, 1, '全量同步', '1', 'sys_sync_mode', '', 'warning', 'Y', '0', 'admin', sysdate(), '', null, '全量同步，会清空目标表数据');
   
   insert into sys_dict_data(dict_code, dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark)
   values(1102, 2, '增量同步', '2', 'sys_sync_mode', '', 'success', 'N', '0', 'admin', sysdate(), '', null, '增量同步，仅同步变化数据');
   ```

3. 更新前端和后端代码
4. 重新编译和部署系统

## 注意事项

- 全量同步会清空目标表数据，请谨慎使用
- 增量同步依赖表有主键，如果表没有主键，将自动使用全量同步方式
- 同步前建议先备份目标数据库 