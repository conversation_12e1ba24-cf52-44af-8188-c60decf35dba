package com.ruoyi.web.controller.system;

import java.util.List;
import java.util.Map;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.SysDockerTemplate;
import com.ruoyi.system.service.ISysDockerTemplateService;
import com.ruoyi.system.utils.DockerMigrationLogger;

/**
 * Docker容器模板Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/docker/template")
public class SysDockerTemplateController extends BaseController
{
    @Autowired
    private ISysDockerTemplateService dockerTemplateService;

    /**
     * 查询Docker容器模板列表
     */
    @PreAuthorize("@ss.hasPermi('datasync:server:manage')")
    @GetMapping("/list")
    public AjaxResult list(SysDockerTemplate dockerTemplate)
    {
        List<SysDockerTemplate> list = dockerTemplateService.selectDockerTemplateList(dockerTemplate);
        return success(list);
    }

    /**
     * 导出Docker容器模板列表
     */
    @PreAuthorize("@ss.hasPermi('datasync:server:manage')")
    @Log(title = "Docker容器模板", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(SysDockerTemplate dockerTemplate)
    {
        List<SysDockerTemplate> list = dockerTemplateService.selectDockerTemplateList(dockerTemplate);
        ExcelUtil<SysDockerTemplate> util = new ExcelUtil<SysDockerTemplate>(SysDockerTemplate.class);
        return util.exportExcel(list, "Docker容器模板数据");
    }

    /**
     * 获取Docker容器模板详细信息
     */
    @PreAuthorize("@ss.hasPermi('datasync:server:manage')")
    @GetMapping(value = "/{templateId}")
    public AjaxResult getInfo(@PathVariable("templateId") Long templateId)
    {
        return success(dockerTemplateService.selectDockerTemplateById(templateId));
    }

    /**
     * 新增Docker容器模板
     */
    @PreAuthorize("@ss.hasPermi('datasync:server:manage')")
    @Log(title = "Docker容器模板", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysDockerTemplate dockerTemplate)
    {
        return toAjax(dockerTemplateService.insertDockerTemplate(dockerTemplate));
    }

    /**
     * 修改Docker容器模板
     */
    @PreAuthorize("@ss.hasPermi('datasync:server:manage')")
    @Log(title = "Docker容器模板", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SysDockerTemplate dockerTemplate)
    {
        return toAjax(dockerTemplateService.updateDockerTemplate(dockerTemplate));
    }

    /**
     * 删除Docker容器模板
     */
    @PreAuthorize("@ss.hasPermi('datasync:server:manage')")
    @Log(title = "Docker容器模板", businessType = BusinessType.DELETE)
    @DeleteMapping("/{templateIds}")
    public AjaxResult remove(@PathVariable Long[] templateIds)
    {
        return toAjax(dockerTemplateService.deleteDockerTemplateByIds(templateIds));
    }
    
    /**
     * 从模板创建Docker容器
     */
    @PreAuthorize("@ss.hasPermi('datasync:server:manage')")
    @Log(title = "从模板创建容器", businessType = BusinessType.OTHER)
    @PostMapping("/create/{serverId}/{templateId}")
    public AjaxResult createContainer(
            @PathVariable("serverId") Long serverId,
            @PathVariable("templateId") Long templateId,
            @RequestBody Map<String, Object> params)
    {
        String containerName = params.containsKey("containerName") ? (String) params.get("containerName") : null;
        @SuppressWarnings("unchecked")
        Map<String, String> customParams = params.containsKey("customParams") ? (Map<String, String>) params.get("customParams") : null;
        
        return success(dockerTemplateService.createContainerFromTemplate(serverId, templateId, containerName, customParams));
    }
    
    /**
     * 获取Docker容器部署日志
     */
    @PreAuthorize("@ss.hasPermi('datasync:server:manage')")
    @GetMapping("/deploy/logs/{serverId}/{processId}")
    public AjaxResult getDeployLogs(
            @PathVariable("serverId") Long serverId,
            @PathVariable("processId") String processId)
    {
        // 从日志系统获取部署日志
        List<String> logs = DockerMigrationLogger.getDeployLogs(serverId, processId);
        
        // 构建返回结果
        AjaxResult result = success();
        result.put("logs", logs);
        result.put("completed", DockerMigrationLogger.isDeployCompleted(serverId, processId));
        result.put("success", DockerMigrationLogger.isDeploySuccess(serverId, processId));
        
        return result;
    }
} 