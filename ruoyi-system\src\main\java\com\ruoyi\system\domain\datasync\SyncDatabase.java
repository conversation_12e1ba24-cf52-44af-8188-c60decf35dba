package com.ruoyi.system.domain.datasync;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import java.util.Date;

/**
 * 数据库对象 sync_database
 * 
 * <AUTHOR>
 */
public class SyncDatabase extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 数据库ID */
    private Long dbId;

    /** 数据库名称 */
    @Excel(name = "数据库名称")
    private String dbName;

    /** 数据库类型（1 MySQL 2 Oracle 3 SQLServer 4 PostgreSQL） */
    @Excel(name = "数据库类型", readConverterExp = "1=MySQL,2=Oracle,3=SQLServer,4=PostgreSQL")
    private String dbType;

    /** 所属服务器ID */
    @Excel(name = "所属服务器ID")
    private Long serverId;

    /** 所属服务器名称 */
    @Excel(name = "所属服务器名称")
    private String serverName;

    /** 端口 */
    @Excel(name = "端口")
    private String dbPort;

    /** 用户名 */
    @Excel(name = "用户名")
    private String dbUsername;

    /** 密码 */
    private String dbPassword;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 是否启用定时备份（0不启用 1启用） */
    @Excel(name = "定时备份", readConverterExp = "0=不启用,1=启用")
    private String scheduleBackup;

    /** 备份周期（1每天 2每周 3每月） */
    @Excel(name = "备份周期", readConverterExp = "1=每天,2=每周,3=每月")
    private String backupCycle;

    /** 备份时间 */
    @Excel(name = "备份时间")
    private String backupTime;

    /** 上次备份时间 */
    @Excel(name = "上次备份时间")
    private Date lastBackupTime;

    /** 备份服务器ID */
    @Excel(name = "备份服务器ID")
    private Long backupServerId;

    /** 备份路径 */
    @Excel(name = "备份路径")
    private String backupPath;

    public void setDbId(Long dbId) 
    {
        this.dbId = dbId;
    }

    public Long getDbId() 
    {
        return dbId;
    }
    public void setDbName(String dbName) 
    {
        this.dbName = dbName;
    }

    public String getDbName() 
    {
        return dbName;
    }
    public void setDbType(String dbType) 
    {
        this.dbType = dbType;
    }

    public String getDbType() 
    {
        return dbType;
    }
    public void setServerId(Long serverId) 
    {
        this.serverId = serverId;
    }

    public Long getServerId() 
    {
        return serverId;
    }
    public void setServerName(String serverName) 
    {
        this.serverName = serverName;
    }

    public String getServerName() 
    {
        return serverName;
    }
    public void setDbPort(String dbPort) 
    {
        this.dbPort = dbPort;
    }

    public String getDbPort() 
    {
        return dbPort;
    }
    public void setDbUsername(String dbUsername) 
    {
        this.dbUsername = dbUsername;
    }

    public String getDbUsername() 
    {
        return dbUsername;
    }
    public void setDbPassword(String dbPassword) 
    {
        this.dbPassword = dbPassword;
    }

    public String getDbPassword() 
    {
        return dbPassword;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    public String getScheduleBackup() {
        return scheduleBackup;
    }

    public void setScheduleBackup(String scheduleBackup) {
        this.scheduleBackup = scheduleBackup;
    }

    public String getBackupCycle() {
        return backupCycle;
    }

    public void setBackupCycle(String backupCycle) {
        this.backupCycle = backupCycle;
    }

    public String getBackupTime() {
        return backupTime;
    }

    public void setBackupTime(String backupTime) {
        this.backupTime = backupTime;
    }

    public Date getLastBackupTime() {
        return lastBackupTime;
    }

    public void setLastBackupTime(Date lastBackupTime) {
        this.lastBackupTime = lastBackupTime;
    }

    public Long getBackupServerId() {
        return backupServerId;
    }

    public void setBackupServerId(Long backupServerId) {
        this.backupServerId = backupServerId;
    }

    public String getBackupPath() {
        return backupPath;
    }

    public void setBackupPath(String backupPath) {
        this.backupPath = backupPath;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("dbId", getDbId())
            .append("dbName", getDbName())
            .append("dbType", getDbType())
            .append("serverId", getServerId())
            .append("serverName", getServerName())
            .append("dbPort", getDbPort())
            .append("dbUsername", getDbUsername())
            .append("dbPassword", getDbPassword())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
} 