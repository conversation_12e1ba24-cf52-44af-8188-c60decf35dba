import request from '@/utils/request'

// 查询服务器列表
export function listServer(query) {
  return request({
    url: '/system/sync/server/list',
    method: 'get',
    params: query
  })
}

// 查询服务器详细
export function getServer(serverId) {
  return request({
    url: '/system/sync/server/' + serverId,
    method: 'get'
  })
}

// 新增服务器
export function addServer(data) {
  return request({
    url: '/system/sync/server',
    method: 'post',
    data: data
  })
}

// 修改服务器
export function updateServer(data) {
  return request({
    url: '/system/sync/server',
    method: 'put',
    data: data
  })
}

// 删除服务器
export function delServer(serverId) {
  return request({
    url: '/system/sync/server/' + serverId,
    method: 'delete'
  })
}

// 测试服务器连接
export function testServerConnection(serverId) {
  return request({
    url: '/system/sync/server/test/' + serverId,
    method: 'get'
  })
}

// 查询服务器可选项
export function listServerOptions() {
  return request({
    url: '/system/sync/server/options',
    method: 'get'
  })
}

// 获取服务器文件列表
export function listServerFiles(serverId, dirPath) {
  return request({
    url: '/system/sync/server/files/' + serverId,
    method: 'get',
    params: { dirPath }
  })
}

// 执行服务器命令
export function executeCommand(serverId, command) {
  return request({
    url: '/system/sync/server/command/' + serverId,
    method: 'post',
    data: { command }
  })
}

// 获取服务器进程列表
export function getServerProcesses(serverId) {
  return request({
    url: '/system/sync/server/processes/' + serverId,
    method: 'get'
  })
}

// 终止服务器进程
export function killServerProcess(serverId, pid) {
  return request({
    url: '/system/sync/server/process/kill/' + serverId,
    method: 'post',
    data: { pid }
  })
}

/**
 * 获取Docker信息
 * @param {number} serverId - 服务器ID
 */
export function getDockerInfo(serverId) {
  return request({
    url: `/system/sync/server/docker/info/${serverId}`,
    method: 'get'
  })
}

/**
 * 获取Docker容器列表
 * @param {number} serverId - 服务器ID
 */
export function getDockerContainers(serverId) {
  return request({
    url: `/system/sync/server/docker/containers/${serverId}`,
    method: 'get'
  })
}

/**
 * 获取Docker镜像列表
 * @param {number} serverId - 服务器ID
 */
export function getDockerImages(serverId) {
  return request({
    url: `/system/sync/server/docker/images/${serverId}`,
    method: 'get'
  })
}

/**
 * 获取Docker Compose文件列表
 * @param {number} serverId - 服务器ID
 * @param {string} dirPath - 目录路径
 */
export function getDockerComposeFiles(serverId, dirPath) {
  return request({
    url: `/system/sync/server/docker/compose/files/${serverId}`,
    method: 'get',
    params: { dirPath }
  })
}

/**
 * 读取Docker Compose文件内容
 * @param {number} serverId - 服务器ID
 * @param {string} filePath - 文件路径
 */
export function readDockerComposeFile(serverId, filePath) {
  return request({
    url: `/system/sync/server/docker/compose/read/${serverId}`,
    method: 'get',
    params: { filePath }
  })
}

/**
 * 迁移Docker容器
 * @param {number} sourceServerId - 源服务器ID
 * @param {number} targetServerId - 目标服务器ID
 * @param {string} containerId - 容器ID，为空时迁移所有容器
 */
export function migrateDockerContainer(sourceServerId, targetServerId, containerId) {
  return request({
    url: '/system/sync/server/docker/migrate/container',
    method: 'post',
    data: { sourceServerId, targetServerId, containerId }
  })
}

/**
 * 迁移Docker Compose配置
 * @param {number} sourceServerId - 源服务器ID
 * @param {number} targetServerId - 目标服务器ID
 * @param {string} composePath - Compose配置目录
 */
export function migrateDockerCompose(sourceServerId, targetServerId, composePath) {
  return request({
    url: '/system/sync/server/docker/migrate/compose',
    method: 'post',
    data: { sourceServerId, targetServerId, composePath }
  })
}

/**
 * 获取可用软件包列表
 * @param {number} serverId - 服务器ID
 * @param {string} keyword - 搜索关键词
 */
export function getAvailableSoftwarePackages(serverId, keyword) {
  return request({
    url: `/system/sync/server/software/available/${serverId}`,
    method: 'get',
    params: { keyword }
  })
}

/**
 * 获取已安装软件包列表
 * @param {number} serverId - 服务器ID
 * @param {string} keyword - 搜索关键词
 */
export function getInstalledSoftwarePackages(serverId, keyword) {
  return request({
    url: `/system/sync/server/software/installed/${serverId}`,
    method: 'get',
    params: { keyword }
  })
}

/**
 * 安装软件包
 * @param {number} serverId - 服务器ID
 * @param {string} packageName - 软件包名称
 */
export function installSoftwarePackage(serverId, packageName) {
  return request({
    url: `/system/sync/server/software/install/${serverId}`,
    method: 'post',
    data: { packageName }
  })
}

/**
 * 卸载软件包
 * @param {number} serverId - 服务器ID
 * @param {string} packageName - 软件包名称
 */
export function uninstallSoftwarePackage(serverId, packageName) {
  return request({
    url: `/system/sync/server/software/uninstall/${serverId}`,
    method: 'post',
    data: { packageName }
  })
}

/**
 * 更新软件包
 * @param {number} serverId - 服务器ID
 * @param {string} packageName - 软件包名称，为空时更新所有软件包
 */
export function updateSoftwarePackage(serverId, packageName) {
  return request({
    url: `/system/sync/server/software/update/${serverId}`,
    method: 'post',
    data: { packageName }
  })
}

/**
 * 一键迁移所有Docker容器
 * @param {number} sourceServerId - 源服务器ID
 * @param {number} targetServerId - 目标服务器ID
 */
export function migrateAllDockerContainers(sourceServerId, targetServerId) {
  return request({
    url: '/system/sync/server/docker/migrate/all-containers',
    method: 'post',
    data: { sourceServerId, targetServerId }
  })
}

/**
 * 迁移Docker镜像
 * @param {number} sourceServerId - 源服务器ID
 * @param {number} targetServerId - 目标服务器ID
 * @param {string} imageId - 镜像ID，为空时迁移所有镜像
 */
export function migrateDockerImage(sourceServerId, targetServerId, imageId) {
  return request({
    url: '/system/sync/server/docker/migrate/image',
    method: 'post',
    data: { sourceServerId, targetServerId, imageId }
  })
}

/**
 * 设置服务器之间的SSH免密登录
 * @param {number} sourceServerId - 源服务器ID
 * @param {number} targetServerId - 目标服务器ID
 */
export function setupSSHKeyAuth(sourceServerId, targetServerId) {
  return request({
    url: '/system/sync/server/ssh/setup-keyauth',
    method: 'post',
    data: { sourceServerId, targetServerId }
  })
}

/**
 * 启动Docker容器
 * @param {number} serverId - 服务器ID
 * @param {string} containerId - 容器ID
 */
export function startDockerContainer(serverId, containerId) {
  return request({
    url: `/system/sync/server/docker/container/start/${serverId}`,
    method: 'post',
    data: { containerId }
  })
}

/**
 * 停止Docker容器
 * @param {number} serverId - 服务器ID
 * @param {string} containerId - 容器ID
 */
export function stopDockerContainer(serverId, containerId) {
  return request({
    url: `/system/sync/server/docker/container/stop/${serverId}`,
    method: 'post',
    data: { containerId }
  })
}

/**
 * 删除Docker容器
 * @param {number} serverId - 服务器ID
 * @param {string} containerId - 容器ID
 */
export function removeDockerContainer(serverId, containerId) {
  return request({
    url: `/system/sync/server/docker/container/remove/${serverId}`,
    method: 'post',
    data: { containerId }
  })
}

/**
 * 获取Docker容器日志
 * @param {number} serverId - 服务器ID
 * @param {string} containerId - 容器ID
 * @param {number} lines - 需要获取的日志行数
 */
export function getDockerContainerLogs(serverId, containerId, lines = 100) {
  return request({
    url: `/system/sync/server/docker/container/logs/${serverId}`,
    method: 'get',
    params: { containerId, lines }
  })
}

// 安装Docker Compose
export function installDockerCompose(serverId, version, installMethod) {
  const data = {
    version: version || undefined,
    installMethod: installMethod || 'user'
  }
  return request({
    url: '/system/sync/server/docker/compose/install/' + serverId,
    method: 'post',
    data: data
  })
}

// 使用Docker Compose部署容器
export function runDockerCompose(serverId, composeFilePath, operation) {
  const data = {
    composeFilePath,
    operation
  }
  return request({
    url: '/system/sync/server/docker/compose/run/' + serverId,
    method: 'post',
    data: data
  })
}

// 生成Docker Compose配置
export function generateDockerCompose(serverId, containerId, outputPath, allContainers) {
  const data = {
    serverId,
    containerId,
    outputPath,
    allContainers
  }
  return request({
    url: '/system/sync/server/docker/compose/generate',
    method: 'post',
    data: data
  })
}
// 将文件从一个服务器同步到另一个服务器
export function syncFileBetweenServers(sourceServerId, targetServerId, sourcePath, targetPath) {
  return request({
    url: '/system/sync/server/file/sync',
    method: 'post',
    data: {
      sourceServerId,
      targetServerId,
      sourcePath,
      targetPath
    }
  })
}

