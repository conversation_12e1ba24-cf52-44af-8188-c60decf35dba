# 若依系统Docker部署指南

本文档提供了使用Docker部署若依系统的详细步骤。

## 前提条件

- 服务器（已有：43.136.49.68）
- 已安装Docker和Docker Compose（部署脚本会自动检查并安装）
- 服务器开放端口：80（前端）、8080（后端）、3306（MySQL）、6379（Redis）

## 部署步骤

### 1. 准备工作

将以下文件上传到服务器：

```
Dockerfile.backend      # 后端服务Dockerfile
Dockerfile.frontend     # 前端服务Dockerfile
docker/nginx.conf       # Nginx配置
docker-compose.yml      # Docker Compose配置
deploy.sh               # 部署脚本
sql/                    # SQL初始化脚本目录
```

### 2. 执行部署

```bash
# 赋予部署脚本执行权限
chmod +x deploy.sh

# 执行部署
./deploy.sh
```

### 3. 验证部署

部署完成后，可以通过以下地址访问系统：

- 前端访问地址：http://43.136.49.68
- 后端API地址：http://43.136.49.68:8080
- 默认管理员账号：admin
- 默认管理员密码：admin123

### 4. 服务管理

```bash
# 查看服务状态
docker-compose ps

# 查看服务日志
docker-compose logs -f

# 重启服务
docker-compose restart

# 停止服务
docker-compose down

# 停止服务并删除数据卷（慎用，会删除数据库数据）
docker-compose down -v
```

## 配置说明

### 数据库配置

- 数据库类型：MySQL 5.7
- 端口：3306
- 用户名：root
- 密码：123456
- 数据库名：ry-vue

### Redis配置

- 版本：Redis 6
- 端口：6379
- 无密码

## 注意事项

1. 首次部署时，MySQL会自动执行`sql`目录下的初始化脚本
2. 数据通过Docker卷进行持久化存储
3. 若需修改配置，请编辑`docker-compose.yml`文件后重新部署
4. 生产环境建议修改默认密码 