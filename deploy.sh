#!/bin/bash

# 确保脚本在出错时退出
set -e

echo "===== 开始部署若依系统 ====="

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "Docker未安装，正在安装..."
    curl -fsSL https://get.docker.com | sh
    sudo systemctl enable docker
    sudo systemctl start docker
else
    echo "Docker已安装"
fi

# 检查Docker Compose是否安装
if ! command -v docker-compose &> /dev/null; then
    echo "Docker Compose未安装，正在安装..."
    sudo curl -L "https://github.com/docker/compose/releases/download/v2.23.3/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
    sudo chmod +x /usr/local/bin/docker-compose
    sudo ln -s /usr/local/bin/docker-compose /usr/bin/docker-compose
else
    echo "Docker Compose已安装"
fi

# 构建并启动服务
echo "开始构建并启动服务..."
docker-compose build
docker-compose up -d

# 检查服务状态
echo "检查服务状态..."
docker-compose ps

echo "===== 若依系统部署完成 ====="
echo "前端访问地址: http://43.136.49.68"
echo "后端API地址: http://43.136.49.68:8080"
echo "MySQL端口: 3306, 用户名: root, 密码: 123456"
echo "Redis端口: 6379, 无密码" 