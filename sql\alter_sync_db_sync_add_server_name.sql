-- 添加服务器名称字段
ALTER TABLE sync_db_sync ADD COLUMN source_server_name varchar(50) DEFAULT NULL COMMENT '源数据库服务器名称';
ALTER TABLE sync_db_sync ADD COLUMN target_server_name varchar(50) DEFAULT NULL COMMENT '目标数据库服务器名称';

-- 更新现有数据的服务器名称
UPDATE sync_db_sync s 
JOIN sync_database sd ON s.source_db_id = sd.db_id
JOIN sync_server ss ON sd.server_id = ss.server_id
SET s.source_server_name = ss.server_name;

UPDATE sync_db_sync s 
JOIN sync_database td ON s.target_db_id = td.db_id
JOIN sync_server ts ON td.server_id = ts.server_id
SET s.target_server_name = ts.server_name; 