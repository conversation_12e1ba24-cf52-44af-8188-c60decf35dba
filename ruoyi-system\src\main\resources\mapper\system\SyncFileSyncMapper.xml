<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SyncFileSyncMapper">
    
    <resultMap type="com.ruoyi.system.domain.datasync.SyncFileSync" id="SyncFileSyncResult">
        <id property="taskId" column="task_id"/>
        <result property="taskName" column="task_name"/>
        <result property="sourceServerId" column="source_server_id"/>
        <result property="sourceServerName" column="source_server_name"/>
        <result property="sourcePath" column="source_path"/>
        <result property="targetServerId" column="target_server_id"/>
        <result property="targetServerName" column="target_server_name"/>
        <result property="targetPath" column="target_path"/>
        <result property="syncType" column="sync_type"/>
        <result property="execCycle" column="exec_cycle"/>
        <result property="execTime" column="exec_time"/>
        <result property="lastExecTime" column="last_exec_time"/>
        <result property="nextExecTime" column="next_exec_time"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectSyncFileSyncVo">
        select fs.task_id, fs.task_name, fs.source_server_id, s1.server_name as source_server_name, 
        fs.source_path, fs.target_server_id, s2.server_name as target_server_name, fs.target_path, 
        fs.sync_type, fs.exec_cycle, fs.exec_time, fs.last_exec_time, fs.next_exec_time, 
        fs.status, fs.create_by, fs.create_time, fs.update_by, fs.update_time, fs.remark 
        from sync_file_sync fs
        left join sync_server s1 on fs.source_server_id = s1.server_id
        left join sync_server s2 on fs.target_server_id = s2.server_id
    </sql>

    <select id="selectSyncFileSyncList" parameterType="com.ruoyi.system.domain.datasync.SyncFileSync" resultMap="SyncFileSyncResult">
        <include refid="selectSyncFileSyncVo"/>
        <where>  
            <if test="taskName != null  and taskName != ''"> and fs.task_name like concat('%', #{taskName}, '%')</if>
            <if test="sourceServerId != null "> and fs.source_server_id = #{sourceServerId}</if>
            <if test="targetServerId != null "> and fs.target_server_id = #{targetServerId}</if>
            <if test="syncType != null  and syncType != ''"> and fs.sync_type = #{syncType}</if>
            <if test="execCycle != null  and execCycle != ''"> and fs.exec_cycle = #{execCycle}</if>
            <if test="status != null  and status != ''"> and fs.status = #{status}</if>
        </where>
    </select>
    
    <select id="selectSyncFileSyncByTaskId" parameterType="Long" resultMap="SyncFileSyncResult">
        <include refid="selectSyncFileSyncVo"/>
        where fs.task_id = #{taskId}
    </select>
        
    <insert id="insertSyncFileSync" parameterType="com.ruoyi.system.domain.datasync.SyncFileSync" useGeneratedKeys="true" keyProperty="taskId">
        insert into sync_file_sync
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskName != null and taskName != ''">task_name,</if>
            <if test="sourceServerId != null">source_server_id,</if>
            <if test="sourcePath != null and sourcePath != ''">source_path,</if>
            <if test="targetServerId != null">target_server_id,</if>
            <if test="targetPath != null and targetPath != ''">target_path,</if>
            <if test="syncType != null and syncType != ''">sync_type,</if>
            <if test="execCycle != null and execCycle != ''">exec_cycle,</if>
            <if test="execTime != null and execTime != ''">exec_time,</if>
            <if test="lastExecTime != null and lastExecTime != ''">last_exec_time,</if>
            <if test="nextExecTime != null and nextExecTime != ''">next_exec_time,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null and remark != ''">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskName != null and taskName != ''">#{taskName},</if>
            <if test="sourceServerId != null">#{sourceServerId},</if>
            <if test="sourcePath != null and sourcePath != ''">#{sourcePath},</if>
            <if test="targetServerId != null">#{targetServerId},</if>
            <if test="targetPath != null and targetPath != ''">#{targetPath},</if>
            <if test="syncType != null and syncType != ''">#{syncType},</if>
            <if test="execCycle != null and execCycle != ''">#{execCycle},</if>
            <if test="execTime != null and execTime != ''">#{execTime},</if>
            <if test="lastExecTime != null and lastExecTime != ''">#{lastExecTime},</if>
            <if test="nextExecTime != null and nextExecTime != ''">#{nextExecTime},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
         </trim>
    </insert>

    <update id="updateSyncFileSync" parameterType="com.ruoyi.system.domain.datasync.SyncFileSync">
        update sync_file_sync
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskName != null and taskName != ''">task_name = #{taskName},</if>
            <if test="sourceServerId != null">source_server_id = #{sourceServerId},</if>
            <if test="sourcePath != null and sourcePath != ''">source_path = #{sourcePath},</if>
            <if test="targetServerId != null">target_server_id = #{targetServerId},</if>
            <if test="targetPath != null and targetPath != ''">target_path = #{targetPath},</if>
            <if test="syncType != null and syncType != ''">sync_type = #{syncType},</if>
            <if test="execCycle != null and execCycle != ''">exec_cycle = #{execCycle},</if>
            <if test="execTime != null and execTime != ''">exec_time = #{execTime},</if>
            <if test="lastExecTime != null and lastExecTime != ''">last_exec_time = #{lastExecTime},</if>
            <if test="nextExecTime != null and nextExecTime != ''">next_exec_time = #{nextExecTime},</if>
            <if test="status != null and status != ''">status = #{status},</if>
            <if test="updateBy != null and updateBy != ''">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where task_id = #{taskId}
    </update>

    <delete id="deleteSyncFileSyncByTaskId" parameterType="Long">
        delete from sync_file_sync where task_id = #{taskId}
    </delete>

    <delete id="deleteSyncFileSyncByTaskIds" parameterType="String">
        delete from sync_file_sync where task_id in 
        <foreach item="taskId" collection="array" open="(" separator="," close=")">
            #{taskId}
        </foreach>
    </delete>
</mapper> 