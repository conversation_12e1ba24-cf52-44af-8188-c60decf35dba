// 测试列名转义方法
public class TestEscapeColumn {
    
    public static String escapeColumnName(String columnName) {
        // MySQL保留关键字列表（常见的）
        String[] reservedWords = {
            "ADD", "ALL", "ALTER", "ANALYZ<PERSON>", "AND", "AS", "ASC", "ASENSITIVE",
            "B<PERSON>OR<PERSON>", "<PERSON><PERSON><PERSON>EE<PERSON>", "BIGIN<PERSON>", "BINARY", "BLOB", "BOTH", "BY",
            "CALL", "CASCADE", "CASE", "CHANGE", "CHAR", "CHARACTER", "CHECK",
            "COLLATE", "COLUMN", "CONDITION", "CONSTRAINT", "CONTINUE", "CONVERT",
            "CREATE", "CROSS", "CURRENT_DATE", "CURRENT_TIME", "CURRENT_TIMESTAMP",
            "CURRENT_USER", "CURSOR", "DATABASE", "<PERSON>AT<PERSON>AS<PERSON>", "DAY_HOUR",
            "DAY_MICROSECOND", "DAY_MINUTE", "DAY_SECOND", "DEC", "DECIMAL",
            "DECLARE", "DEFAULT", "DELAYED", "DELETE", "DESC", "DESCRIBE",
            "DETERMINISTIC", "DISTINCT", "DISTINCTROW", "DIV", "DOUBLE", "DROP",
            "DUAL", "EACH", "ELSE", "ELSEIF", "ENCLOSED", "ESCAPED", "EXISTS",
            "EXIT", "EXPLAIN", "FALSE", "FETCH", "FLOAT", "FLOAT4", "FLOAT8",
            "FOR", "FORCE", "FOREIGN", "FROM", "FULLTEXT", "GRANT", "GROUP",
            "HAVING", "HIGH_PRIORITY", "HOUR_MICROSECOND", "HOUR_MINUTE",
            "HOUR_SECOND", "IF", "IGNORE", "IN", "INDEX", "INFILE", "INNER",
            "INOUT", "INSENSITIVE", "INSERT", "INT", "INT1", "INT2", "INT3",
            "INT4", "INT8", "INTEGER", "INTERVAL", "INTO", "IS", "ITERATE",
            "JOIN", "KEY", "KEYS", "KILL", "LEADING", "LEAVE", "LEFT", "LIKE",
            "LIMIT", "LINEAR", "LINES", "LOAD", "LOCALTIME", "LOCALTIMESTAMP",
            "LOCK", "LONG", "LONGBLOB", "LONGTEXT", "LOOP", "LOW_PRIORITY",
            "MATCH", "MEDIUMBLOB", "MEDIUMINT", "MEDIUMTEXT", "MIDDLEINT",
            "MINUTE_MICROSECOND", "MINUTE_SECOND", "MOD", "MODIFIES", "NATURAL",
            "NOT", "NO_WRITE_TO_BINLOG", "NULL", "NUMERIC", "ON", "OPTIMIZE",
            "OPTION", "OPTIONALLY", "OR", "ORDER", "OUT", "OUTER", "OUTFILE",
            "PRECISION", "PRIMARY", "PROCEDURE", "PURGE", "RAID0", "RANGE",
            "READ", "READS", "REAL", "REFERENCES", "REGEXP", "RELEASE", "RENAME",
            "REPEAT", "REPLACE", "REQUIRE", "RESTRICT", "RETURN", "REVOKE",
            "RIGHT", "RLIKE", "SCHEMA", "SCHEMAS", "SECOND_MICROSECOND", "SELECT",
            "SENSITIVE", "SEPARATOR", "SET", "SHOW", "SMALLINT", "SPATIAL",
            "SPECIFIC", "SQL", "SQLEXCEPTION", "SQLSTATE", "SQLWARNING",
            "SQL_BIG_RESULT", "SQL_CALC_FOUND_ROWS", "SQL_SMALL_RESULT", "SSL",
            "STARTING", "STRAIGHT_JOIN", "TABLE", "TERMINATED", "THEN", "TINYBLOB",
            "TINYINT", "TINYTEXT", "TO", "TRAILING", "TRIGGER", "TRUE", "UNDO",
            "UNION", "UNIQUE", "UNLOCK", "UNSIGNED", "UPDATE", "USAGE", "USE",
            "USING", "UTC_DATE", "UTC_TIME", "UTC_TIMESTAMP", "VALUES", "VARBINARY",
            "VARCHAR", "VARCHARACTER", "VARYING", "WHEN", "WHERE", "WHILE",
            "WITH", "WRITE", "X509", "XOR", "YEAR_MONTH", "ZEROFILL"
        };
        
        // 检查是否为保留关键字（不区分大小写）
        for (String reserved : reservedWords) {
            if (reserved.equalsIgnoreCase(columnName)) {
                return "`" + columnName + "`";
            }
        }
        
        // 如果不是保留关键字，直接返回原列名
        return columnName;
    }
    
    public static void main(String[] args) {
        // 测试保留关键字
        System.out.println("测试 change:");
        System.out.println(escapeColumnName("change")); // 应该输出: `change`
        
        System.out.println("测试 CHANGE:");
        System.out.println(escapeColumnName("CHANGE")); // 应该输出: `CHANGE`
        
        System.out.println("测试 order:");
        System.out.println(escapeColumnName("order")); // 应该输出: `order`
        
        System.out.println("测试 普通列名:");
        System.out.println(escapeColumnName("id")); // 应该输出: id
        
        System.out.println("测试 user_name:");
        System.out.println(escapeColumnName("user_name")); // 应该输出: user_name
    }
}
