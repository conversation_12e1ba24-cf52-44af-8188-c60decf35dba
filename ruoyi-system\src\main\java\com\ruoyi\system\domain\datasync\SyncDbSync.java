package com.ruoyi.system.domain.datasync;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 数据库同步对象 sync_db_sync
 * 
 * <AUTHOR>
 */
public class SyncDbSync extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 同步ID */
    private Long syncId;

    /** 同步任务名称 */
    @Excel(name = "同步任务名称")
    private String syncName;

    /** 源数据库ID */
    @Excel(name = "源数据库ID")
    private Long sourceDbId;

    /** 源数据库名称 */
    @Excel(name = "源数据库名称")
    private String sourceDbName;

    /** 目标数据库ID */
    @Excel(name = "目标数据库ID")
    private Long targetDbId;

    /** 目标数据库名称 */
    @Excel(name = "目标数据库名称")
    private String targetDbName;

    /** 源数据库服务器名称 */
    @Excel(name = "源数据库服务器")
    private String sourceServerName;

    /** 目标数据库服务器名称 */
    @Excel(name = "目标数据库服务器")
    private String targetServerName;

    /** 同步类型（0手动同步 1自动同步） */
    @Excel(name = "同步类型", readConverterExp = "0=手动同步,1=自动同步")
    private String syncType;

    /** 同步方式（1全量同步 2增量同步） */
    @Excel(name = "同步方式", readConverterExp = "1=全量同步,2=增量同步")
    private String syncMode;

    /** 同步表 */
    @Excel(name = "同步表")
    private String syncTables;

    /** 执行周期（0手动 1每天 2每周 3每月） */
    @Excel(name = "执行周期", readConverterExp = "0=手动,1=每天,2=每周,3=每月")
    private String execCycle;

    /** 执行时间 */
    @Excel(name = "执行时间")
    private String execTime;

    /** 最近执行时间 */
    @Excel(name = "最近执行时间")
    private String lastExecTime;

    /** 下次执行时间 */
    @Excel(name = "下次执行时间")
    private String nextExecTime;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    public void setSyncId(Long syncId) 
    {
        this.syncId = syncId;
    }

    public Long getSyncId() 
    {
        return syncId;
    }
    public void setSyncName(String syncName) 
    {
        this.syncName = syncName;
    }

    public String getSyncName() 
    {
        return syncName;
    }
    public void setSourceDbId(Long sourceDbId) 
    {
        this.sourceDbId = sourceDbId;
    }

    public Long getSourceDbId() 
    {
        return sourceDbId;
    }
    public void setSourceDbName(String sourceDbName) 
    {
        this.sourceDbName = sourceDbName;
    }

    public String getSourceDbName() 
    {
        return sourceDbName;
    }
    public void setTargetDbId(Long targetDbId) 
    {
        this.targetDbId = targetDbId;
    }

    public Long getTargetDbId() 
    {
        return targetDbId;
    }
    public void setTargetDbName(String targetDbName) 
    {
        this.targetDbName = targetDbName;
    }

    public String getTargetDbName() 
    {
        return targetDbName;
    }
    public void setSourceServerName(String sourceServerName) 
    {
        this.sourceServerName = sourceServerName;
    }

    public String getSourceServerName() 
    {
        return sourceServerName;
    }
    public void setTargetServerName(String targetServerName) 
    {
        this.targetServerName = targetServerName;
    }

    public String getTargetServerName() 
    {
        return targetServerName;
    }
    public void setSyncType(String syncType) 
    {
        this.syncType = syncType;
    }

    public String getSyncType() 
    {
        return syncType;
    }
    public void setSyncMode(String syncMode) 
    {
        this.syncMode = syncMode;
    }

    public String getSyncMode() 
    {
        return syncMode;
    }
    public void setSyncTables(String syncTables) 
    {
        this.syncTables = syncTables;
    }

    public String getSyncTables() 
    {
        return syncTables;
    }
    public void setExecCycle(String execCycle) 
    {
        this.execCycle = execCycle;
    }

    public String getExecCycle() 
    {
        return execCycle;
    }
    public void setExecTime(String execTime) 
    {
        this.execTime = execTime;
    }

    public String getExecTime() 
    {
        return execTime;
    }
    public void setLastExecTime(String lastExecTime) 
    {
        this.lastExecTime = lastExecTime;
    }

    public String getLastExecTime() 
    {
        return lastExecTime;
    }
    public void setNextExecTime(String nextExecTime) 
    {
        this.nextExecTime = nextExecTime;
    }

    public String getNextExecTime() 
    {
        return nextExecTime;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("syncId", getSyncId())
            .append("syncName", getSyncName())
            .append("sourceDbId", getSourceDbId())
            .append("sourceDbName", getSourceDbName())
            .append("targetDbId", getTargetDbId())
            .append("targetDbName", getTargetDbName())
            .append("sourceServerName", getSourceServerName())
            .append("targetServerName", getTargetServerName())
            .append("syncType", getSyncType())
            .append("syncMode", getSyncMode())
            .append("syncTables", getSyncTables())
            .append("execCycle", getExecCycle())
            .append("execTime", getExecTime())
            .append("lastExecTime", getLastExecTime())
            .append("nextExecTime", getNextExecTime())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
} 