package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.SysDockerTemplate;

/**
 * Docker容器模板 数据层
 * 
 * <AUTHOR>
 */
public interface SysDockerTemplateMapper 
{
    /**
     * 查询Docker容器模板
     * 
     * @param templateId Docker容器模板ID
     * @return Docker容器模板
     */
    public SysDockerTemplate selectDockerTemplateById(Long templateId);

    /**
     * 查询Docker容器模板列表
     * 
     * @param dockerTemplate Docker容器模板
     * @return Docker容器模板集合
     */
    public List<SysDockerTemplate> selectDockerTemplateList(SysDockerTemplate dockerTemplate);

    /**
     * 新增Docker容器模板
     * 
     * @param dockerTemplate Docker容器模板
     * @return 结果
     */
    public int insertDockerTemplate(SysDockerTemplate dockerTemplate);

    /**
     * 修改Docker容器模板
     * 
     * @param dockerTemplate Docker容器模板
     * @return 结果
     */
    public int updateDockerTemplate(SysDockerTemplate dockerTemplate);

    /**
     * 删除Docker容器模板
     * 
     * @param templateId Docker容器模板ID
     * @return 结果
     */
    public int deleteDockerTemplateById(Long templateId);

    /**
     * 批量删除Docker容器模板
     * 
     * @param templateIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteDockerTemplateByIds(Long[] templateIds);
} 