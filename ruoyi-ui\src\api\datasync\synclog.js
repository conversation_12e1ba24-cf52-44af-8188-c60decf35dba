import request from '@/utils/request'

// 查询同步日志列表
export function listSyncLog(query) {
  return request({
    url: '/datasync/synclog/list',
    method: 'get',
    params: query
  })
}

// 查询指定同步任务的日志列表
export function listSyncLogBySyncId(syncId) {
  return request({
    url: '/datasync/synclog/listBySyncId/' + syncId,
    method: 'get'
  })
}

// 查询同步日志详细
export function getSyncLog(logId) {
  return request({
    url: '/datasync/synclog/' + logId,
    method: 'get'
  })
}

// 删除同步日志
export function delSyncLog(logId) {
  return request({
    url: '/datasync/synclog/' + logId,
    method: 'delete'
  })
}

// 清空指定同步任务的日志
export function cleanSyncLogBySyncId(syncId) {
  return request({
    url: '/datasync/synclog/clean/' + syncId,
    method: 'delete'
  })
}

// 导出同步日志
export function exportSyncLog(query) {
  return request({
    url: '/datasync/synclog/export',
    method: 'post',
    params: query
  })
} 