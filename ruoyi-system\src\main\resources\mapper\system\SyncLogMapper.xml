<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SyncLogMapper">
    
    <resultMap type="com.ruoyi.system.domain.datasync.SyncLog" id="SyncLogResult">
        <id     property="logId"      column="log_id"      />
        <result property="syncId"     column="sync_id"     />
        <result property="execTime"   column="exec_time"   />
        <result property="success"    column="success"     />
        <result property="message"    column="message"     />
    </resultMap>

    <sql id="selectSyncLogVo">
        select log_id, sync_id, exec_time, success, message from sync_log
    </sql>

    <select id="selectSyncLogList" parameterType="com.ruoyi.system.domain.datasync.SyncLog" resultMap="SyncLogResult">
        <include refid="selectSyncLogVo"/>
        <where>  
            <if test="syncId != null "> and sync_id = #{syncId}</if>
            <if test="execTime != null "> and exec_time = #{execTime}</if>
            <if test="success != null  and success != ''"> and success = #{success}</if>
        </where>
        order by exec_time desc
    </select>
    
    <select id="selectSyncLogByLogId" parameterType="Long" resultMap="SyncLogResult">
        <include refid="selectSyncLogVo"/>
        where log_id = #{logId}
    </select>
    
    <select id="selectSyncLogBySyncId" parameterType="Long" resultMap="SyncLogResult">
        <include refid="selectSyncLogVo"/>
        where sync_id = #{syncId}
        order by exec_time desc
    </select>
        
    <insert id="insertSyncLog" parameterType="com.ruoyi.system.domain.datasync.SyncLog" useGeneratedKeys="true" keyProperty="logId">
        insert into sync_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="syncId != null">sync_id,</if>
            <if test="execTime != null">exec_time,</if>
            <if test="success != null">success,</if>
            <if test="message != null">message,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="syncId != null">#{syncId},</if>
            <if test="execTime != null">#{execTime},</if>
            <if test="success != null">#{success},</if>
            <if test="message != null">#{message},</if>
         </trim>
    </insert>

    <delete id="deleteSyncLogByLogId" parameterType="Long">
        delete from sync_log where log_id = #{logId}
    </delete>

    <delete id="deleteSyncLogByLogIds" parameterType="String">
        delete from sync_log where log_id in 
        <foreach item="logId" collection="array" open="(" separator="," close=")">
            #{logId}
        </foreach>
    </delete>
    
    <delete id="deleteSyncLogBySyncId" parameterType="Long">
        delete from sync_log where sync_id = #{syncId}
    </delete>
</mapper> 