package com.ruoyi.web.controller.datasync;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.datasync.SyncFileSync;
import com.ruoyi.system.service.ISyncFileSyncService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.system.domain.datasync.SyncFileSyncLog;

/**
 * 文件同步Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/datasync/filesync")
public class SyncFileSyncController extends BaseController
{
    private static final Logger log = LoggerFactory.getLogger(SyncFileSyncController.class);
    
    @Autowired
    private ISyncFileSyncService syncFileSyncService;

    /**
     * 查询文件同步列表
     */
    @PreAuthorize("@ss.hasPermi('datasync:filesync:list')")
    @GetMapping("/list")
    public TableDataInfo list(SyncFileSync syncFileSync)
    {
        log.info("接收查询文件同步列表请求: {}", syncFileSync);
        startPage();
        List<SyncFileSync> list = syncFileSyncService.selectSyncFileSyncList(syncFileSync);
        TableDataInfo dataTable = getDataTable(list);
        log.info("返回文件同步列表数据: 总记录数={}, 页面大小={}", dataTable.getTotal(), dataTable.getRows().size());
        return dataTable;
    }

    /**
     * 导出文件同步列表
     */
    @PreAuthorize("@ss.hasPermi('datasync:filesync:export')")
    @Log(title = "文件同步", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SyncFileSync syncFileSync)
    {
        log.info("接收导出文件同步列表请求: {}", syncFileSync);
        List<SyncFileSync> list = syncFileSyncService.selectSyncFileSyncList(syncFileSync);
        log.info("准备导出文件同步数据: 记录数={}", list.size());
        ExcelUtil<SyncFileSync> util = new ExcelUtil<SyncFileSync>(SyncFileSync.class);
        util.exportExcel(response, list, "文件同步数据");
        log.info("文件同步数据导出完成");
    }

    /**
     * 获取文件同步详细信息
     */
    @PreAuthorize("@ss.hasPermi('datasync:filesync:query')")
    @GetMapping(value = "/{taskId}")
    public AjaxResult getInfo(@PathVariable("taskId") Long taskId)
    {
        log.info("接收获取文件同步详情请求: taskId={}", taskId);
        SyncFileSync syncFileSync = syncFileSyncService.selectSyncFileSyncByTaskId(taskId);
        log.info("返回文件同步详情: {}", syncFileSync != null ? "成功" : "未找到记录");
        return success(syncFileSync);
    }

    /**
     * 新增文件同步
     */
    @PreAuthorize("@ss.hasPermi('datasync:filesync:add')")
    @Log(title = "文件同步", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SyncFileSync syncFileSync)
    {
        log.info("接收新增文件同步请求: {}", syncFileSync);
        int rows = syncFileSyncService.insertSyncFileSync(syncFileSync);
        log.info("新增文件同步结果: 影响行数={}, 新ID={}", rows, syncFileSync.getTaskId());
        return toAjax(rows);
    }

    /**
     * 修改文件同步
     */
    @PreAuthorize("@ss.hasPermi('datasync:filesync:edit')")
    @Log(title = "文件同步", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SyncFileSync syncFileSync)
    {
        log.info("接收修改文件同步请求: taskId={}", syncFileSync.getTaskId());
        int rows = syncFileSyncService.updateSyncFileSync(syncFileSync);
        log.info("修改文件同步结果: 影响行数={}", rows);
        return toAjax(rows);
    }

    /**
     * 删除文件同步
     */
    @PreAuthorize("@ss.hasPermi('datasync:filesync:remove')")
    @Log(title = "文件同步", businessType = BusinessType.DELETE)
    @DeleteMapping("/{taskIds}")
    public AjaxResult remove(@PathVariable Long[] taskIds)
    {
        log.info("接收删除文件同步请求: taskIds={}", taskIds);
        int rows = syncFileSyncService.deleteSyncFileSyncByTaskIds(taskIds);
        log.info("删除文件同步结果: 影响行数={}", rows);
        return toAjax(rows);
    }
    
    /**
     * 执行文件同步任务
     */
    @PreAuthorize("@ss.hasPermi('datasync:filesync:run')")
    @Log(title = "文件同步", businessType = BusinessType.OTHER)
    @GetMapping("/run/{taskId}")
    public AjaxResult runSync(@PathVariable("taskId") Long taskId)
    {
        log.info("接收执行文件同步任务请求: taskId={}", taskId);
        String result = syncFileSyncService.runSyncFileTask(taskId);
        log.info("执行文件同步任务结果: {}", result);
        return success(result);
    }
    
    /**
     * 获取同步任务日志列表
     */
    @PreAuthorize("@ss.hasPermi('datasync:filesync:query')")
    @GetMapping("/logs/{taskId}")
    public AjaxResult getSyncLogs(@PathVariable("taskId") Long taskId)
    {
        log.info("接收获取同步任务日志列表请求: taskId={}", taskId);
        List<SyncFileSyncLog> logs = syncFileSyncService.selectSyncFileSyncLogsByTaskId(taskId);
        log.info("获取同步任务日志列表结果: 共{}条记录", logs.size());
        return AjaxResult.success(logs);
    }
    
    /**
     * 获取同步日志详情
     */
    @PreAuthorize("@ss.hasPermi('datasync:filesync:query')")
    @GetMapping("/log/detail/{logId}")
    public AjaxResult getSyncLogDetail(@PathVariable("logId") Long logId)
    {
        log.info("接收获取同步日志详情请求: logId={}", logId);
        String logDetail = syncFileSyncService.getSyncFileSyncLogDetail(logId);
        return AjaxResult.success(logDetail);
    }
} 