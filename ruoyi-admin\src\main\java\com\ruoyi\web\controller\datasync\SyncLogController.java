package com.ruoyi.web.controller.datasync;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.datasync.SyncLog;
import com.ruoyi.system.service.ISyncLogService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 同步日志Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/datasync/synclog")
public class SyncLogController extends BaseController
{
    @Autowired
    private ISyncLogService syncLogService;

    /**
     * 查询同步日志列表
     */
    @PreAuthorize("@ss.hasPermi('datasync:synclog:list')")
    @GetMapping("/list")
    public TableDataInfo list(SyncLog syncLog)
    {
        startPage();
        List<SyncLog> list = syncLogService.selectSyncLogList(syncLog);
        return getDataTable(list);
    }

    /**
     * 获取指定同步任务的日志列表
     */
    @PreAuthorize("@ss.hasPermi('datasync:synclog:list')")
    @GetMapping("/listBySyncId/{syncId}")
    public TableDataInfo listBySyncId(@PathVariable("syncId") Long syncId)
    {
        startPage();
        List<SyncLog> list = syncLogService.selectSyncLogBySyncId(syncId);
        return getDataTable(list);
    }

    /**
     * 导出同步日志列表
     */
    @PreAuthorize("@ss.hasPermi('datasync:synclog:export')")
    @Log(title = "同步日志", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SyncLog syncLog)
    {
        List<SyncLog> list = syncLogService.selectSyncLogList(syncLog);
        ExcelUtil<SyncLog> util = new ExcelUtil<SyncLog>(SyncLog.class);
        util.exportExcel(response, list, "同步日志数据");
    }

    /**
     * 获取同步日志详细信息
     */
    @PreAuthorize("@ss.hasPermi('datasync:synclog:query')")
    @GetMapping(value = "/{logId}")
    public AjaxResult getInfo(@PathVariable("logId") Long logId)
    {
        return success(syncLogService.selectSyncLogByLogId(logId));
    }

    /**
     * 删除同步日志
     */
    @PreAuthorize("@ss.hasPermi('datasync:synclog:remove')")
    @Log(title = "同步日志", businessType = BusinessType.DELETE)
    @DeleteMapping("/{logIds}")
    public AjaxResult remove(@PathVariable Long[] logIds)
    {
        return toAjax(syncLogService.deleteSyncLogByLogIds(logIds));
    }
    
    /**
     * 清空指定同步任务的日志
     */
    @PreAuthorize("@ss.hasPermi('datasync:synclog:remove')")
    @Log(title = "同步日志", businessType = BusinessType.DELETE)
    @DeleteMapping("/clean/{syncId}")
    public AjaxResult cleanBySyncId(@PathVariable("syncId") Long syncId)
    {
        return toAjax(syncLogService.deleteSyncLogBySyncId(syncId));
    }
} 