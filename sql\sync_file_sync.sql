-- ----------------------------
-- 1、文件同步表
-- ----------------------------
drop table if exists sync_file_sync;
create table sync_file_sync (
  task_id           bigint(20)      not null auto_increment    comment '任务ID',
  task_name         varchar(100)    not null                   comment '任务名称',
  source_server_id  bigint(20)      not null                   comment '源服务器ID',
  source_path       varchar(500)    not null                   comment '源路径',
  target_server_id  bigint(20)      not null                   comment '目标服务器ID',
  target_path       varchar(500)    not null                   comment '目标路径',
  sync_type         char(1)         default '1'                comment '同步类型（1全量同步 2增量同步）',
  exec_cycle        char(1)         default '0'                comment '执行周期（0手动执行 1每天 2每周 3每月）',
  exec_time         varchar(20)                                comment '执行时间',
  last_exec_time    varchar(50)                                comment '最近执行时间',
  next_exec_time    varchar(50)                                comment '下次执行时间',
  status            char(1)         default '0'                comment '状态（0正常 1停用）',
  create_by         varchar(64)     default ''                 comment '创建者',
  create_time       datetime                                   comment '创建时间',
  update_by         varchar(64)     default ''                 comment '更新者',
  update_time       datetime                                   comment '更新时间',
  remark            varchar(500)    default null               comment '备注',
  primary key (task_id)
) engine=innodb auto_increment=100 comment = '文件同步表';

-- ----------------------------
-- 2、菜单权限
-- ----------------------------
-- 文件同步菜单
insert into sys_menu values('1083', '文件同步', '1080', '3', 'filesync', 'datasync/filesync/index', '', 1, 0, 'C', '0', '0', 'datasync:filesync:list', 'job', 'admin', sysdate(), '', null, '文件同步菜单');
-- 文件同步按钮
insert into sys_menu values('1083001', '文件同步查询', '1083', '1', '#', '', '', 1, 0, 'F', '0', '0', 'datasync:filesync:query', '#', 'admin', sysdate(), '', null, '');
insert into sys_menu values('1083002', '文件同步新增', '1083', '2', '#', '', '', 1, 0, 'F', '0', '0', 'datasync:filesync:add', '#', 'admin', sysdate(), '', null, '');
insert into sys_menu values('1083003', '文件同步修改', '1083', '3', '#', '', '', 1, 0, 'F', '0', '0', 'datasync:filesync:edit', '#', 'admin', sysdate(), '', null, '');
insert into sys_menu values('1083004', '文件同步删除', '1083', '4', '#', '', '', 1, 0, 'F', '0', '0', 'datasync:filesync:remove', '#', 'admin', sysdate(), '', null, '');
insert into sys_menu values('1083005', '文件同步导出', '1083', '5', '#', '', '', 1, 0, 'F', '0', '0', 'datasync:filesync:export', '#', 'admin', sysdate(), '', null, '');
insert into sys_menu values('1083006', '文件同步执行', '1083', '6', '#', '', '', 1, 0, 'F', '0', '0', 'datasync:filesync:run', '#', 'admin', sysdate(), '', null, ''); 