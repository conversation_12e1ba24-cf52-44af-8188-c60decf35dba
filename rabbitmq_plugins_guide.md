# RabbitMQ 插件管理指南

## RabbitMQ 插件概述

RabbitMQ 支持多种插件来扩展其功能，常用插件包括：

- `rabbitmq_management`: Web管理界面
- `rabbitmq_shovel`: 在broker间转发消息
- `rabbitmq_federation`: 提供跨broker的消息复制
- `rabbitmq_mqtt`: MQTT协议支持
- `rabbitmq_stomp`: STOMP协议支持
- `rabbitmq_web_stomp`: WebStomp支持
- `rabbitmq_auth_backend_ldap`: LDAP认证

## 在Docker容器中启用插件的方法

### 方法一：使用启用插件文件

1. 创建一个`enabled_plugins`文件，列出要启用的插件：

```
# 创建enabled_plugins文件
cat > enabled_plugins << EOF
[rabbitmq_management,rabbitmq_mqtt,rabbitmq_federation,rabbitmq_stomp].
EOF
```

2. 在启动容器时挂载此文件：

```bash
docker run -d --name rabbitmq \
  -p 5672:5672 -p 15672:15672 \
  -v $(pwd)/enabled_plugins:/etc/rabbitmq/enabled_plugins \
  -v $(pwd)/data:/var/lib/rabbitmq \
  -e RABBITMQ_DEFAULT_USER=admin \
  -e RABBITMQ_DEFAULT_PASS=admin123 \
  rabbitmq:management
```

### 方法二：在运行中的容器内启用插件

1. 进入运行中的RabbitMQ容器：

```bash
docker exec -it rabbitmq bash
```

2. 使用`rabbitmq-plugins`命令启用插件：

```bash
rabbitmq-plugins enable rabbitmq_mqtt
rabbitmq-plugins enable rabbitmq_federation
rabbitmq-plugins enable rabbitmq_stomp
```

### 方法三：修改Docker模板的启动命令

修改Docker模板，在启动命令中添加插件启用步骤：

```sql
-- 修改RabbitMQ模板，添加插件启用命令
INSERT INTO sys_docker_template (
    template_name, template_description, image_name, image_tag, 
    port_mappings, volume_mappings, environment_vars, network_mode, 
    restart_policy, command, is_system, use_compose, 
    create_by, create_time, remark
) VALUES (
    'RabbitMQ消息队列(带插件)', 
    'RabbitMQ是实现了高级消息队列协议（AMQP）的开源消息代理软件，预装常用插件',
    'rabbitmq', 
    'management', 
    '5672:5672,15672:15672,1883:1883,61613:61613', 
    '/data/rabbitmq/data:/var/lib/rabbitmq,/data/rabbitmq/log:/var/log/rabbitmq', 
    'RABBITMQ_DEFAULT_USER=admin,RABBITMQ_DEFAULT_PASS=admin123', 
    'bridge', 
    'always', 
    'sh -c "rabbitmq-plugins enable rabbitmq_mqtt rabbitmq_federation rabbitmq_stomp && rabbitmq-server"', 
    '1', 
    '0', 
    'admin', 
    sysdate(), 
    'RabbitMQ服务，默认端口5672，管理界面15672，用户名/密码：admin/admin123，已启用MQTT、Federation和STOMP插件'
);
```

注意：上述模板添加了MQTT(1883)和STOMP(61613)端口映射。

## 常用插件端口说明

| 插件 | 端口 | 说明 |
|------|------|------|
| rabbitmq_management | 15672 | Web管理界面 |
| rabbitmq_mqtt | 1883 | MQTT协议 |
| rabbitmq_stomp | 61613 | STOMP协议 |
| rabbitmq_web_mqtt | 15675 | Web MQTT |
| rabbitmq_web_stomp | 15674 | Web STOMP |

## 创建预配置的插件文件

如果您想预先配置多个插件，可以创建一个`enabled_plugins`文件：

```
# 创建包含常用插件的配置文件
cat > enabled_plugins << EOF
[rabbitmq_management,rabbitmq_mqtt,rabbitmq_federation,rabbitmq_stomp,rabbitmq_shovel].
EOF
```

然后在部署容器时挂载此文件：

```bash
docker run -d --name rabbitmq \
  -p 5672:5672 -p 15672:15672 -p 1883:1883 -p 61613:61613 \
  -v $(pwd)/enabled_plugins:/etc/rabbitmq/enabled_plugins \
  -v $(pwd)/data:/var/lib/rabbitmq \
  -e RABBITMQ_DEFAULT_USER=admin \
  -e RABBITMQ_DEFAULT_PASS=admin123 \
  rabbitmq:management
```

## 检查已启用的插件

进入容器并执行以下命令查看已启用的插件：

```bash
docker exec -it rabbitmq rabbitmq-plugins list
```

带有`[E*]`标记的插件表示已启用。 