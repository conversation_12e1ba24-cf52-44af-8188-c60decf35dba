-- RabbitMQ模板（预配置插件版本 - 修复版）
INSERT INTO sys_docker_template (
    template_name, template_description, image_name, image_tag, 
    port_mappings, volume_mappings, environment_vars, network_mode, 
    restart_policy, command, is_system, use_compose, 
    create_by, create_time, remark
) VALUES (
    'RabbitMQ消息队列(带插件)', 
    'RabbitMQ是实现了高级消息队列协议（AMQP）的开源消息代理软件，预装常用插件',
    'rabbitmq', 
    'management', 
    '5672:5672,15672:15672,1883:1883,61613:61613', 
    '/data/rabbitmq/data:/var/lib/rabbitmq,/data/rabbitmq/log:/var/log/rabbitmq', 
    'RABBITMQ_DEFAULT_USER=admin,RABBITMQ_DEFAULT_PASS=admin123', 
    'bridge', 
    'always', 
    'sh -c "rabbitmq-plugins enable rabbitmq_mqtt rabbitmq_federation rabbitmq_stomp rabbitmq_shovel && rabbitmq-server"', 
    '1', 
    '0', 
    'admin', 
    sysdate(), 
    'RabbitMQ服务，默认端口5672，管理界面15672，已启用MQTT(1883)、STOMP(61613)、Federation和Shovel插件'
);

-- 部署说明
/*
错误原因分析：
原模板中的问题是尝试将主机上的文件(/data/rabbitmq/enabled_plugins)挂载到容器内的文件(/etc/rabbitmq/enabled_plugins)，
而Docker只支持将主机上的目录挂载到容器内的目录，或将主机上的文件挂载到容器内不存在的路径。

解决方案：
1. 移除了enabled_plugins文件的挂载
2. 改为在启动命令中直接启用所需插件

部署步骤：
1. 确保目录存在：
   mkdir -p /data/rabbitmq/data /data/rabbitmq/log

2. 部署容器：
   使用上述模板部署RabbitMQ容器

3. 验证插件是否启用：
   docker exec -it rabbitmq rabbitmq-plugins list

注意：如果使用host网络模式，端口映射会被忽略，因为容器直接使用主机网络。
*/ 