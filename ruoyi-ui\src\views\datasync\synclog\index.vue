<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="任务ID" prop="syncId">
        <el-input
          v-model="queryParams.syncId"
          placeholder="请输入同步任务ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="是否成功" prop="success">
        <el-select v-model="queryParams.success" placeholder="请选择是否成功" clearable>
          <el-option
            v-for="dict in dict.type.sys_yes_no"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="执行时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['datasync:synclog:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['datasync:synclog:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="syncLogList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="日志ID" align="center" prop="logId" v-if="false" />
      <el-table-column label="同步任务ID" align="center" prop="syncId" />
      <el-table-column label="执行时间" align="center" prop="execTime" width="160">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.execTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="是否成功" align="center" prop="success">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_yes_no" :value="scope.row.success" />
        </template>
      </el-table-column>
      <el-table-column label="日志消息" align="center" prop="message" :show-overflow-tooltip="true" width="300" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['datasync:synclog:query']"
          >查看</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['datasync:synclog:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 查看同步日志详情对话框 -->
    <el-dialog title="日志详情" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" label-width="80px">
        <el-form-item label="同步任务ID">
          <el-input v-model="form.syncId" readonly />
        </el-form-item>
        <el-form-item label="执行时间">
          <el-input v-model="execTimeStr" readonly />
        </el-form-item>
        <el-form-item label="是否成功">
          <el-tag :type="form.success === '0' ? 'success' : 'danger'">
            {{ form.success === '0' ? '成功' : '失败' }}
          </el-tag>
        </el-form-item>
        <el-form-item label="日志消息">
          <el-input v-model="form.message" type="textarea" :rows="5" readonly />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="open = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listSyncLog, getSyncLog, delSyncLog, exportSyncLog } from "@/api/datasync/synclog";

export default {
  name: "SyncLog",
  dicts: ['sys_yes_no'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 同步日志表格数据
      syncLogList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        syncId: null,
        success: null
      },
      // 表单参数
      form: {},
      // 时间格式化字符串
      execTimeStr: ""
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询同步日志列表 */
    getList() {
      this.loading = true;
      listSyncLog(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.syncLogList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        logId: null,
        syncId: null,
        execTime: null,
        success: null,
        message: null
      };
      this.execTimeStr = "";
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.logId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 查看按钮操作 */
    handleView(row) {
      this.reset();
      const logId = row.logId || this.ids[0];
      getSyncLog(logId).then(response => {
        this.form = response.data;
        this.execTimeStr = this.parseTime(response.data.execTime);
        this.open = true;
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const logIds = row.logId || this.ids;
      this.$modal.confirm('是否确认删除同步日志编号为"' + logIds + '"的数据项？').then(function() {
        return delSyncLog(logIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.$modal.confirm('是否确认导出所有同步日志数据项？').then(() => {
        this.exportLoading = true;
        return exportSyncLog(this.queryParams);
      }).then(response => {
        this.download(response.msg);
        this.exportLoading = false;
      }).catch(() => {});
    }
  }
};
</script> 