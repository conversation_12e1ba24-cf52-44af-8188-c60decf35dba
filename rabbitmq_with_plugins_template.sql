-- RabbitMQ模板（带插件简化版）
INSERT INTO sys_docker_template (
    template_name, template_description, image_name, image_tag, 
    port_mappings, volume_mappings, environment_vars, network_mode, 
    restart_policy, command, is_system, use_compose, 
    create_by, create_time, remark
) VALUES (
    'RabbitMQ消息队列(带插件)', 
    'RabbitMQ是实现了高级消息队列协议（AMQP）的开源消息代理软件，预装常用插件',
    'rabbitmq', 
    'management', 
    '5672:5672,15672:15672,1883:1883,61613:61613', 
    '/data/rabbitmq/data:/var/lib/rabbitmq', 
    'RABBITMQ_DEFAULT_USER=admin,RABBITMQ_DEFAULT_PASS=admin123', 
    'bridge', 
    'always', 
    'sh -c "rabbitmq-plugins enable rabbitmq_mqtt rabbitmq_federation rabbitmq_stomp rabbitmq_shovel && rabbitmq-server"', 
    '1', 
    '0', 
    'admin', 
    sysdate(), 
    'RabbitMQ服务，默认端口5672，管理界面15672，MQTT端口1883，STOMP端口61613，预装常用插件'
);

-- 部署说明
/*
带插件版RabbitMQ模板说明：

1. 此模板预装了以下插件：
   - rabbitmq_mqtt：MQTT协议支持
   - rabbitmq_federation：跨broker消息复制
   - rabbitmq_stomp：STOMP协议支持
   - rabbitmq_shovel：在broker间转发消息

2. 端口说明：
   - 5672：AMQP协议端口
   - 15672：管理界面端口
   - 1883：MQTT协议端口
   - 61613：STOMP协议端口

3. 部署前准备：
   - 确保目录存在：mkdir -p /data/rabbitmq/data
   - 确保目录权限正确：chmod -R 777 /data/rabbitmq

4. 部署后访问管理界面：
   - 地址：http://服务器IP:15672
   - 用户名：admin
   - 密码：admin123

5. 如果部署失败，请尝试使用不带插件的简化版模板
*/ 