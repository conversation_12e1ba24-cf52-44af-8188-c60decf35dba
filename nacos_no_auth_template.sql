INSERT INTO `sys_docker_template` VALUES (5, 'Nacos配置中心(无认证)', 'Nacos 是阿里巴巴推出来的一个新开源项目，是一个更易于构建云原生应用的动态服务发现、配置管理和服务管理平台', 'nacos', 'latest', '8848:8848', '/data/nacos/logs:/home/<USER>/logs,/data/nacos/data:/home/<USER>/data', 'MODE=standalone,PREFER_HOST_MODE=hostname,SPRING_DATASOURCE_PLATFORM=mysql,MYSQL_SERVICE_HOST=数据库IP,MYSQL_SERVICE_PORT=3306,MYSQL_SERVICE_DB_NAME=nacos,MYSQL_SERVICE_USER=数据库用户名,MYSQL_SERVICE_PASSWORD=数据库密码,NACOS_AUTH_ENABLE=false,JVM_XMS=512m,JVM_XMX=512m,JVM_XMN=256m', '', 'bridge', 'always', 1, 'admin', '2023-05-01 10:00:00', NULL, NULL, '部署前准备：\n1. 创建数据目录：mkdir -p /data/nacos/logs /data/nacos/data\n2. 设置目录权限：chmod -R 777 /data/nacos\n3. 准备MySQL数据库，创建nacos数据库并导入初始SQL脚本\n   (脚本地址：https://github.com/alibaba/nacos/blob/master/distribution/conf/mysql-schema.sql)\n4. 部署时请将环境变量中的数据库连接信息替换为实际值\n\n部署后访问：\nhttp://服务器IP:8848/nacos\n用户名/密码：nacos/nacos (无认证模式下不需要登录)\n\n注意事项：\n1. 无认证模式下，任何人都可以访问Nacos控制台和API\n2. 生产环境建议开启认证\n3. 如需集群部署，请修改MODE环境变量为cluster并配置集群列表'); 