-- ----------------------------
-- Table structure for sys_docker_template
-- ----------------------------
DROP TABLE IF EXISTS `sys_docker_template`;
CREATE TABLE `sys_docker_template` (
  `template_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '模板ID',
  `template_name` varchar(50) NOT NULL COMMENT '模板名称',
  `template_description` varchar(500) DEFAULT NULL COMMENT '模板描述',
  `image_name` varchar(255) NOT NULL COMMENT '镜像名称',
  `image_tag` varchar(50) DEFAULT 'latest' COMMENT '镜像标签',
  `port_mappings` varchar(500) DEFAULT NULL COMMENT '端口映射(格式: 宿主机端口:容器端口,分隔)',
  `volume_mappings` varchar(1000) DEFAULT NULL COMMENT '卷映射(格式: 宿主机路径:容器路径,分隔)',
  `environment_vars` varchar(2000) DEFAULT NULL COMMENT '环境变量(格式: 变量名=变量值,分隔)',
  `network_mode` varchar(50) DEFAULT 'bridge' COMMENT '网络模式',
  `restart_policy` varchar(50) DEFAULT 'unless-stopped' COMMENT '重启策略',
  `command` varchar(1000) DEFAULT NULL COMMENT '启动命令',
  `is_system` char(1) DEFAULT '0' COMMENT '是否系统模板(0否 1是)',
  `use_compose` char(1) DEFAULT '0' COMMENT '是否使用Docker Compose(0否 1是)',
  `compose_config` text DEFAULT NULL COMMENT 'Docker Compose配置',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`template_id`),
  UNIQUE KEY `idx_template_name` (`template_name`)
) ENGINE=InnoDB AUTO_INCREMENT=100 COMMENT='Docker容器模板';

-- ----------------------------
-- Records of sys_docker_template
-- ----------------------------
INSERT INTO `sys_docker_template` VALUES (1, 'Redis', 'Redis数据库容器', 'redis', 'latest', '6379:6379', '/data/redis:/data', 'REDIS_PASSWORD=123456', 'bridge', 'unless-stopped', NULL, '1', '0', NULL, 'admin', sysdate(), '', NULL, 'Redis数据库默认模板');
INSERT INTO `sys_docker_template` VALUES (2, 'MySQL', 'MySQL数据库容器', 'mysql', '5.7', '3306:3306', '/data/mysql:/var/lib/mysql', 'MYSQL_ROOT_PASSWORD=123456', 'bridge', 'unless-stopped', NULL, '1', '0', NULL, 'admin', sysdate(), '', NULL, 'MySQL数据库默认模板');
INSERT INTO `sys_docker_template` VALUES (3, 'Nginx', 'Nginx Web服务器', 'nginx', 'latest', '80:80,443:443', '/data/nginx/html:/usr/share/nginx/html,/data/nginx/conf:/etc/nginx/conf.d', '', 'bridge', 'unless-stopped', NULL, '1', '0', NULL, 'admin', sysdate(), '', NULL, 'Nginx Web服务器默认模板');

-- 添加一个Docker Compose示例模板
INSERT INTO `sys_docker_template` VALUES (4, 'WordPress', 'WordPress博客系统(Compose)', 'wordpress', 'latest', NULL, NULL, NULL, NULL, NULL, NULL, '1', '1', 
'version: "3"\n\nservices:\n  db:\n    image: mysql:5.7\n    volumes:\n      - db_data:/var/lib/mysql\n    restart: always\n    environment:\n      MYSQL_ROOT_PASSWORD: wordpress\n      MYSQL_DATABASE: wordpress\n      MYSQL_USER: wordpress\n      MYSQL_PASSWORD: wordpress\n\n  wordpress:\n    depends_on:\n      - db\n    image: wordpress:latest\n    ports:\n      - "8080:80"\n    restart: always\n    environment:\n      WORDPRESS_DB_HOST: db:3306\n      WORDPRESS_DB_USER: wordpress\n      WORDPRESS_DB_PASSWORD: wordpress\n      WORDPRESS_DB_NAME: wordpress\n    volumes:\n      - wordpress_data:/var/www/html\n\nvolumes:\n  db_data:\n  wordpress_data:', 
'admin', sysdate(), '', NULL, 'WordPress博客系统Compose模板示例'); 