-- 修改sync_db_sync表，添加sync_mode字段
ALTER TABLE sync_db_sync ADD COLUMN sync_mode char(1) DEFAULT '1' COMMENT '同步方式（1全量同步 2增量同步）';

-- 修改同步类型字段的注释
ALTER TABLE sync_db_sync MODIFY COLUMN sync_type char(1) COMMENT '同步类型（0手动同步 1自动同步）';

-- 更新所有现有数据，设置默认同步方式为全量同步
UPDATE sync_db_sync SET sync_mode = '1' WHERE sync_mode IS NULL;

-- 更新所有现有数据，修改同步类型的值
UPDATE sync_db_sync SET sync_type = '0' WHERE sync_type = '1'; -- 将全量同步改为手动同步
UPDATE sync_db_sync SET sync_type = '1' WHERE sync_type = '2'; -- 将增量同步改为自动同步 