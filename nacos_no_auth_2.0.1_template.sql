INSERT INTO `sys_docker_template` VALUES (5, 'Nacos配置中心(2.0.1无认证)', 'Nacos 是阿里巴巴推出来的一个新开源项目，是一个更易于构建云原生应用的动态服务发现、配置管理和服务管理平台', 'nacos/nacos-server', '2.0.1', '8848:8848,9848:9848,9849:9849', '/data/nacos/logs:/home/<USER>/logs,/data/nacos/data:/home/<USER>/data', 'MODE=standalone,PREFER_HOST_MODE=hostname,NACOS_AUTH_ENABLE=false,JVM_XMS=512m,JVM_XMX=512m,JVM_XMN=256m', '', 'bridge', 'always', 1, 'admin', '2023-05-01 10:00:00', NULL, NULL, '部署前准备：\n1. 创建数据目录：mkdir -p /data/nacos/logs /data/nacos/data\n2. 设置目录权限：chmod -R 777 /data/nacos\n\n部署后访问：\nhttp://服务器IP:8848/nacos\n用户名/密码：nacos/nacos (无认证模式下不需要登录)\n\n注意事项：\n1. 此模板使用Nacos 2.0.1版本，无需配置数据库，使用内嵌数据库\n2. 无认证模式下，任何人都可以访问Nacos控制台和API\n3. 生产环境建议开启认证和配置外部MySQL数据库\n4. 如需配置外部MySQL数据库，请添加以下环境变量：\n   SPRING_DATASOURCE_PLATFORM=mysql\n   MYSQL_SERVICE_HOST=数据库IP\n   MYSQL_SERVICE_PORT=3306\n   MYSQL_SERVICE_DB_NAME=nacos\n   MYSQL_SERVICE_USER=数据库用户名\n   MYSQL_SERVICE_PASSWORD=数据库密码\n5. 如需集群部署，请修改MODE环境变量为cluster并配置集群列表'); 