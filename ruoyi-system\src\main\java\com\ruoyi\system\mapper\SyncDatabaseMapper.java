package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.datasync.SyncDatabase;

/**
 * 数据库Mapper接口
 * 
 * <AUTHOR>
 */
public interface SyncDatabaseMapper 
{
    /**
     * 查询数据库
     * 
     * @param dbId 数据库主键
     * @return 数据库
     */
    public SyncDatabase selectSyncDatabaseByDbId(Long dbId);

    /**
     * 查询数据库列表
     * 
     * @param syncDatabase 数据库
     * @return 数据库集合
     */
    public List<SyncDatabase> selectSyncDatabaseList(SyncDatabase syncDatabase);

    /**
     * 新增数据库
     * 
     * @param syncDatabase 数据库
     * @return 结果
     */
    public int insertSyncDatabase(SyncDatabase syncDatabase);

    /**
     * 修改数据库
     * 
     * @param syncDatabase 数据库
     * @return 结果
     */
    public int updateSyncDatabase(SyncDatabase syncDatabase);

    /**
     * 删除数据库
     * 
     * @param dbId 数据库主键
     * @return 结果
     */
    public int deleteSyncDatabaseByDbId(Long dbId);

    /**
     * 批量删除数据库
     * 
     * @param dbIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSyncDatabaseByDbIds(Long[] dbIds);
} 