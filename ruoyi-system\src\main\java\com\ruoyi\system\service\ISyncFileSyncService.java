package com.ruoyi.system.service;

import java.util.List;
import com.ruoyi.system.domain.datasync.SyncFileSync;
import com.ruoyi.system.domain.datasync.SyncFileSyncLog;

/**
 * 文件同步Service接口
 * 
 * <AUTHOR>
 */
public interface ISyncFileSyncService 
{
    /**
     * 查询文件同步
     * 
     * @param taskId 文件同步主键
     * @return 文件同步
     */
    public SyncFileSync selectSyncFileSyncByTaskId(Long taskId);

    /**
     * 查询文件同步列表
     * 
     * @param syncFileSync 文件同步
     * @return 文件同步集合
     */
    public List<SyncFileSync> selectSyncFileSyncList(SyncFileSync syncFileSync);

    /**
     * 新增文件同步
     * 
     * @param syncFileSync 文件同步
     * @return 结果
     */
    public int insertSyncFileSync(SyncFileSync syncFileSync);

    /**
     * 修改文件同步
     * 
     * @param syncFileSync 文件同步
     * @return 结果
     */
    public int updateSyncFileSync(SyncFileSync syncFileSync);

    /**
     * 批量删除文件同步
     * 
     * @param taskIds 需要删除的文件同步主键集合
     * @return 结果
     */
    public int deleteSyncFileSyncByTaskIds(Long[] taskIds);

    /**
     * 删除文件同步信息
     * 
     * @param taskId 文件同步主键
     * @return 结果
     */
    public int deleteSyncFileSyncByTaskId(Long taskId);
    
    /**
     * 执行文件同步任务
     * 
     * @param taskId 文件同步任务ID
     * @return 结果
     */
    public String runSyncFileTask(Long taskId);
    
    /**
     * 查询文件同步日志列表
     * 
     * @param taskId 文件同步任务ID
     * @return 日志列表
     */
    public List<SyncFileSyncLog> selectSyncFileSyncLogsByTaskId(Long taskId);
    
    /**
     * 获取同步日志详情
     * 
     * @param logId 日志ID
     * @return 日志详情
     */
    public String getSyncFileSyncLogDetail(Long logId);
} 