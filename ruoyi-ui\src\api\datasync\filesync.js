import request from '@/utils/request'

// 查询文件同步列表
export function listFileSync(query) {
  return request({
    url: '/datasync/filesync/list',
    method: 'get',
    params: query
  })
}

// 查询文件同步详细
export function getFileSync(taskId) {
  return request({
    url: '/datasync/filesync/' + taskId,
    method: 'get'
  })
}

// 新增文件同步
export function addFileSync(data) {
  return request({
    url: '/datasync/filesync',
    method: 'post',
    data: data
  })
}

// 修改文件同步
export function updateFileSync(data) {
  return request({
    url: '/datasync/filesync',
    method: 'put',
    data: data
  })
}

// 删除文件同步
export function delFileSync(taskId) {
  return request({
    url: '/datasync/filesync/' + taskId,
    method: 'delete'
  })
}

// 立即执行同步任务
export function runFileSync(taskId) {
  return request({
    url: '/datasync/filesync/run/' + taskId,
    method: 'get'
  })
}

// 获取同步日志列表
export function listSyncLogs(taskId) {
  return request({
    url: '/datasync/filesync/logs/' + taskId,
    method: 'get'
  })
}

// 获取同步日志详情
export function getSyncLogDetail(logId) {
  return request({
    url: '/datasync/filesync/log/detail/' + logId,
    method: 'get'
  })
} 