<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.SyncDbSyncMapper">
    
    <resultMap type="com.ruoyi.system.domain.datasync.SyncDbSync" id="SyncDbSyncResult">
        <result property="syncId"    column="sync_id"    />
        <result property="syncName"    column="sync_name"    />
        <result property="sourceDbId"    column="source_db_id"    />
        <result property="sourceDbName"    column="source_db_name"    />
        <result property="targetDbId"    column="target_db_id"    />
        <result property="targetDbName"    column="target_db_name"    />
        <result property="syncType"    column="sync_type"    />
        <result property="syncTables"    column="sync_tables"    />
        <result property="execCycle"    column="exec_cycle"    />
        <result property="execTime"    column="exec_time"    />
        <result property="lastExecTime"    column="last_exec_time"    />
        <result property="nextExecTime"    column="next_exec_time"    />
        <result property="status"    column="status"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
        <result property="syncMode"    column="sync_mode"    />
        <result property="sourceServerName"    column="source_server_name"    />
        <result property="targetServerName"    column="target_server_name"    />
    </resultMap>

    <sql id="selectSyncDbSyncVo">
        select s.sync_id, s.sync_name, s.source_db_id, d1.db_name as source_db_name, 
        s.target_db_id, d2.db_name as target_db_name, s.source_server_name, s.target_server_name, 
        s.sync_type, s.sync_mode, s.sync_tables, s.exec_cycle, s.exec_time, s.last_exec_time, 
        s.next_exec_time, s.status, s.create_by, s.create_time, s.update_by, s.update_time, s.remark
        from sync_db_sync s
        left join sync_database d1 on s.source_db_id = d1.db_id
        left join sync_database d2 on s.target_db_id = d2.db_id
    </sql>

    <select id="selectSyncDbSyncList" parameterType="com.ruoyi.system.domain.datasync.SyncDbSync" resultMap="SyncDbSyncResult">
        <include refid="selectSyncDbSyncVo"/>
        <where>  
            <if test="syncName != null  and syncName != ''"> and s.sync_name like concat('%', #{syncName}, '%')</if>
            <if test="sourceDbId != null "> and s.source_db_id = #{sourceDbId}</if>
            <if test="targetDbId != null "> and s.target_db_id = #{targetDbId}</if>
            <if test="syncType != null  and syncType != ''"> and s.sync_type = #{syncType}</if>
            <if test="execCycle != null  and execCycle != ''"> and s.exec_cycle = #{execCycle}</if>
            <if test="status != null  and status != ''"> and s.status = #{status}</if>
        </where>
    </select>
    
    <select id="selectSyncDbSyncBySyncId" parameterType="Long" resultMap="SyncDbSyncResult">
        <include refid="selectSyncDbSyncVo"/>
        where s.sync_id = #{syncId}
    </select>
        
    <insert id="insertSyncDbSync" parameterType="com.ruoyi.system.domain.datasync.SyncDbSync" useGeneratedKeys="true" keyProperty="syncId">
        insert into sync_db_sync
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="syncName != null">sync_name,</if>
            <if test="sourceDbId != null">source_db_id,</if>
            <if test="sourceDbName != null">source_db_name,</if>
            <if test="targetDbId != null">target_db_id,</if>
            <if test="targetDbName != null">target_db_name,</if>
            <if test="sourceServerName != null">source_server_name,</if>
            <if test="targetServerName != null">target_server_name,</if>
            <if test="syncType != null">sync_type,</if>
            <if test="syncMode != null">sync_mode,</if>
            <if test="syncTables != null">sync_tables,</if>
            <if test="execCycle != null">exec_cycle,</if>
            <if test="execTime != null">exec_time,</if>
            <if test="lastExecTime != null">last_exec_time,</if>
            <if test="nextExecTime != null">next_exec_time,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="syncName != null">#{syncName},</if>
            <if test="sourceDbId != null">#{sourceDbId},</if>
            <if test="sourceDbName != null">#{sourceDbName},</if>
            <if test="targetDbId != null">#{targetDbId},</if>
            <if test="targetDbName != null">#{targetDbName},</if>
            <if test="sourceServerName != null">#{sourceServerName},</if>
            <if test="targetServerName != null">#{targetServerName},</if>
            <if test="syncType != null">#{syncType},</if>
            <if test="syncMode != null">#{syncMode},</if>
            <if test="syncTables != null">#{syncTables},</if>
            <if test="execCycle != null">#{execCycle},</if>
            <if test="execTime != null">#{execTime},</if>
            <if test="lastExecTime != null">#{lastExecTime},</if>
            <if test="nextExecTime != null">#{nextExecTime},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateSyncDbSync" parameterType="com.ruoyi.system.domain.datasync.SyncDbSync">
        update sync_db_sync
        <trim prefix="SET" suffixOverrides=",">
            <if test="syncName != null">sync_name = #{syncName},</if>
            <if test="sourceDbId != null">source_db_id = #{sourceDbId},</if>
            <if test="sourceDbName != null">source_db_name = #{sourceDbName},</if>
            <if test="targetDbId != null">target_db_id = #{targetDbId},</if>
            <if test="targetDbName != null">target_db_name = #{targetDbName},</if>
            <if test="sourceServerName != null">source_server_name = #{sourceServerName},</if>
            <if test="targetServerName != null">target_server_name = #{targetServerName},</if>
            <if test="syncType != null">sync_type = #{syncType},</if>
            <if test="syncMode != null">sync_mode = #{syncMode},</if>
            <if test="syncTables != null">sync_tables = #{syncTables},</if>
            <if test="execCycle != null">exec_cycle = #{execCycle},</if>
            <if test="execTime != null">exec_time = #{execTime},</if>
            <if test="lastExecTime != null">last_exec_time = #{lastExecTime},</if>
            <if test="nextExecTime != null">next_exec_time = #{nextExecTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where sync_id = #{syncId}
    </update>

    <delete id="deleteSyncDbSyncBySyncId" parameterType="Long">
        delete from sync_db_sync where sync_id = #{syncId}
    </delete>

    <delete id="deleteSyncDbSyncBySyncIds" parameterType="String">
        delete from sync_db_sync where sync_id in 
        <foreach item="syncId" collection="array" open="(" separator="," close=")">
            #{syncId}
        </foreach>
    </delete>
</mapper> 