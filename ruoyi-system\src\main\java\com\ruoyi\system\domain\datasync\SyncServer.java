package com.ruoyi.system.domain.datasync;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 服务器对象 sync_server
 * 
 * <AUTHOR>
 */
public class SyncServer extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 服务器ID */
    private Long serverId;

    /** 服务器名称 */
    @Excel(name = "服务器名称")
    private String serverName;

    /** IP地址 */
    @Excel(name = "IP地址")
    private String ipAddress;

    /** 端口 */
    @Excel(name = "端口")
    private String port;

    /** 用户名 */
    @Excel(name = "用户名")
    private String userName;

    /** 密码 */
    private String password;

    /** 备份路径 */
    @Excel(name = "备份路径")
    private String backupPath;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    public void setServerId(Long serverId) 
    {
        this.serverId = serverId;
    }

    public Long getServerId() 
    {
        return serverId;
    }
    public void setServerName(String serverName) 
    {
        this.serverName = serverName;
    }

    public String getServerName() 
    {
        return serverName;
    }
    public void setIpAddress(String ipAddress) 
    {
        this.ipAddress = ipAddress;
    }

    public String getIpAddress() 
    {
        return ipAddress;
    }
    public void setPort(String port) 
    {
        this.port = port;
    }

    public String getPort() 
    {
        return port;
    }
    public void setUserName(String userName) 
    {
        this.userName = userName;
    }

    public String getUserName() 
    {
        return userName;
    }
    public void setPassword(String password) 
    {
        this.password = password;
    }

    public String getPassword() 
    {
        return password;
    }
    public void setBackupPath(String backupPath) 
    {
        this.backupPath = backupPath;
    }

    public String getBackupPath() 
    {
        return backupPath;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("serverId", getServerId())
            .append("serverName", getServerName())
            .append("ipAddress", getIpAddress())
            .append("port", getPort())
            .append("userName", getUserName())
            .append("password", getPassword())
            .append("backupPath", getBackupPath())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
} 