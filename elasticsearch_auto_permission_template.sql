-- Elasticsearch模板（自动权限处理版）
INSERT INTO sys_docker_template (
    template_name, template_description, image_name, image_tag, 
    port_mappings, volume_mappings, environment_vars, network_mode, 
    restart_policy, command, is_system, use_compose, 
    create_by, create_time, remark
) VALUES (
    'Elasticsearch搜索引擎(自动权限版)', 
    'Elasticsearch是一个分布式、RESTful风格的搜索和数据分析引擎，自动处理目录权限',
    'elasticsearch', 
    '7.17.9', 
    '9200:9200,9300:9300', 
    '/data/elasticsearch/data:/usr/share/elasticsearch/data', 
    'discovery.type=single-node,ES_JAVA_OPTS="-Xms512m -Xmx512m",xpack.security.enabled=false', 
    'bridge', 
    'always', 
    NULL, 
    '1', 
    '0', 
    'admin', 
    sysdate(), 
    'Elasticsearch 7.17.9，默认端口9200，自动处理目录权限和虚拟内存设置'
);

-- 部署说明
/*
Elasticsearch自动权限处理版模板说明：

特点：
1. 系统会自动创建挂载目录并设置适当权限
2. 自动配置Elasticsearch所需的虚拟内存参数
3. 简化的环境变量配置，避免Docker命令行参数错误

部署说明：
1. 系统会自动执行：
   - mkdir -p /data/elasticsearch/data
   - chmod -R 777 /data/elasticsearch/data
   - sysctl -w vm.max_map_count=262144

2. 部署后访问：
   - 地址：http://服务器IP:9200
   - 检查是否返回正确的JSON响应

3. 常见问题排查：
   - 如果仍然无法启动，检查服务器内存是否足够（至少需要2GB）
   - 检查服务器是否允许执行sysctl命令（需要root权限）
   - 检查挂载目录是否有足够的磁盘空间

4. 手动部署命令参考：
   docker run -d --name es \
     -p 9200:9200 -p 9300:9300 \
     -v /data/elasticsearch/data:/usr/share/elasticsearch/data \
     -e "discovery.type=single-node" \
     -e "ES_JAVA_OPTS=-Xms512m -Xmx512m" \
     -e "xpack.security.enabled=false" \
     --network bridge \
     --restart always \
     elasticsearch:7.17.9
*/ 