package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.datasync.SyncFileSync;

/**
 * 文件同步Mapper接口
 * 
 * <AUTHOR>
 */
public interface SyncFileSyncMapper 
{
    /**
     * 查询文件同步
     * 
     * @param taskId 文件同步主键
     * @return 文件同步
     */
    public SyncFileSync selectSyncFileSyncByTaskId(Long taskId);

    /**
     * 查询文件同步列表
     * 
     * @param syncFileSync 文件同步
     * @return 文件同步集合
     */
    public List<SyncFileSync> selectSyncFileSyncList(SyncFileSync syncFileSync);

    /**
     * 新增文件同步
     * 
     * @param syncFileSync 文件同步
     * @return 结果
     */
    public int insertSyncFileSync(SyncFileSync syncFileSync);

    /**
     * 修改文件同步
     * 
     * @param syncFileSync 文件同步
     * @return 结果
     */
    public int updateSyncFileSync(SyncFileSync syncFileSync);

    /**
     * 删除文件同步
     * 
     * @param taskId 文件同步主键
     * @return 结果
     */
    public int deleteSyncFileSyncByTaskId(Long taskId);

    /**
     * 批量删除文件同步
     * 
     * @param taskIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSyncFileSyncByTaskIds(Long[] taskIds);
} 