-- 数据同步-服务器表
drop table if exists sync_server;
create table sync_server (
  server_id       bigint(20)      not null auto_increment    comment '服务器ID',
  server_name     varchar(50)     not null                   comment '服务器名称',
  ip_address      varchar(50)     not null                   comment 'IP地址',
  port            varchar(10)     not null                   comment '端口',
  user_name       varchar(30)     not null                   comment '用户名',
  password        varchar(100)    not null                   comment '密码',
  status          char(1)         default '0'                comment '状态（0正常 1停用）',
  create_by       varchar(64)     default ''                 comment '创建者',
  create_time     datetime                                   comment '创建时间',
  update_by       varchar(64)     default ''                 comment '更新者',
  update_time     datetime                                   comment '更新时间',
  remark          varchar(500)    default null               comment '备注',
  primary key (server_id)
) engine=innodb auto_increment=100 comment = '同步服务器表';

-- 菜单 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('服务器管理', (select menu_id from sys_menu where menu_name = '数据同步'), 1, 'server', 'datasync/server/index', 1, 0, 'C', '0', '0', 'datasync:server:list', 'server', 'admin', sysdate(), '', null, '服务器管理菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('服务器查询', @parentId, 1, '#', '', 1, 0, 'F', '0', '0', 'datasync:server:query', '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('服务器新增', @parentId, 2, '#', '', 1, 0, 'F', '0', '0', 'datasync:server:add', '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('服务器修改', @parentId, 3, '#', '', 1, 0, 'F', '0', '0', 'datasync:server:edit', '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('服务器删除', @parentId, 4, '#', '', 1, 0, 'F', '0', '0', 'datasync:server:remove', '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('服务器导出', @parentId, 5, '#', '', 1, 0, 'F', '0', '0', 'datasync:server:export', '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('服务器测试', @parentId, 6, '#', '', 1, 0, 'F', '0', '0', 'datasync:server:test', '#', 'admin', sysdate(), '', null, ''); 