package com.ruoyi.system.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.SyncLogMapper;
import com.ruoyi.system.domain.datasync.SyncLog;
import com.ruoyi.system.service.ISyncLogService;

/**
 * 同步日志Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class SyncLogServiceImpl implements ISyncLogService 
{
    @Autowired
    private SyncLogMapper syncLogMapper;

    /**
     * 查询同步日志
     * 
     * @param logId 同步日志主键
     * @return 同步日志
     */
    @Override
    public SyncLog selectSyncLogByLogId(Long logId)
    {
        return syncLogMapper.selectSyncLogByLogId(logId);
    }

    /**
     * 查询同步日志列表
     * 
     * @param syncLog 同步日志
     * @return 同步日志
     */
    @Override
    public List<SyncLog> selectSyncLogList(SyncLog syncLog)
    {
        return syncLogMapper.selectSyncLogList(syncLog);
    }

    /**
     * 查询指定同步任务的日志列表
     * 
     * @param syncId 同步任务ID
     * @return 同步日志集合
     */
    @Override
    public List<SyncLog> selectSyncLogBySyncId(Long syncId)
    {
        return syncLogMapper.selectSyncLogBySyncId(syncId);
    }

    /**
     * 新增同步日志
     * 
     * @param syncLog 同步日志
     * @return 结果
     */
    @Override
    public int insertSyncLog(SyncLog syncLog)
    {
        return syncLogMapper.insertSyncLog(syncLog);
    }

    /**
     * 删除同步日志
     * 
     * @param logId 同步日志主键
     * @return 结果
     */
    @Override
    public int deleteSyncLogByLogId(Long logId)
    {
        return syncLogMapper.deleteSyncLogByLogId(logId);
    }

    /**
     * 批量删除同步日志
     * 
     * @param logIds 需要删除的同步日志主键
     * @return 结果
     */
    @Override
    public int deleteSyncLogByLogIds(Long[] logIds)
    {
        return syncLogMapper.deleteSyncLogByLogIds(logIds);
    }
    
    /**
     * 删除指定同步任务的日志
     * 
     * @param syncId 同步任务ID
     * @return 结果
     */
    @Override
    public int deleteSyncLogBySyncId(Long syncId)
    {
        return syncLogMapper.deleteSyncLogBySyncId(syncId);
    }
} 