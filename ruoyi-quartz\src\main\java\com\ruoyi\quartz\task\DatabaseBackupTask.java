package com.ruoyi.quartz.task;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import com.ruoyi.system.service.ISyncDatabaseService;
import com.ruoyi.common.utils.StringUtils;

/**
 * 数据库定时备份任务
 * 
 * <AUTHOR>
 */
@Component("databaseBackupTask")
public class DatabaseBackupTask
{
    @Autowired
    private ISyncDatabaseService syncDatabaseService;

    /**
     * 执行数据库定时备份
     */
    public void executeBackup()
    {
        syncDatabaseService.executeScheduledBackup();
    }
} 