-- 更新数据库管理菜单权限

-- 获取数据库管理菜单ID
SET @dbMenuId := (SELECT menu_id FROM sys_menu WHERE menu_name = '数据库管理' AND path = 'database');

-- 添加数据库备份按钮权限
INSERT INTO sys_menu(menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('数据库备份', @dbMenuId, 6, '#', '', 1, 0, 'F', '0', '0', 'system:database:backup', '#', 'admin', sysdate(), '', null, '');

-- 添加数据库查询按钮权限
INSERT INTO sys_menu(menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
VALUES('数据库查询', @dbMenuId, 7, '#', '', 1, 0, 'F', '0', '0', 'system:database:query', '#', 'admin', sysdate(), '', null, '');

-- 获取管理员角色ID
SET @adminRoleId := (SELECT role_id FROM sys_role WHERE role_key = 'admin');

-- 获取新增菜单ID
SET @backupMenuId := (SELECT menu_id FROM sys_menu WHERE menu_name = '数据库备份' AND parent_id = @dbMenuId);
SET @queryMenuId := (SELECT menu_id FROM sys_menu WHERE menu_name = '数据库查询' AND parent_id = @dbMenuId);

-- 分配权限给管理员角色
INSERT INTO sys_role_menu(role_id, menu_id) VALUES(@adminRoleId, @backupMenuId);
INSERT INTO sys_role_menu(role_id, menu_id) VALUES(@adminRoleId, @queryMenuId); 