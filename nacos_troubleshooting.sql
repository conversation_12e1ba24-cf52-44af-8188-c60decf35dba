-- Nacos服务注册中心模板（自动配置版）
INSERT INTO sys_docker_template (
    template_name, template_description, image_name, image_tag, 
    port_mappings, volume_mappings, environment_vars, network_mode, 
    restart_policy, command, is_system, use_compose, 
    create_by, create_time, remark
) VALUES (
    'Nacos服务注册中心(自动配置版)', 
    'Nacos是阿里巴巴开源的服务注册与发现、配置管理和服务管理平台',
    'nacos', 
    'latest', 
    '8848:8848,9848:9848,9849:9849', 
    '/data/nacos/logs:/home/<USER>/logs,/data/nacos/conf:/home/<USER>/conf', 
    'MODE=standalone,PREFER_HOST_MODE=hostname,JVM_XMS=256m,JVM_XMX=512m,JVM_XMN=128m', 
    'bridge', 
    'always', 
    NULL, 
    '1', 
    '0', 
    'admin', 
    sysdate(), 
    'Nacos服务注册中心，默认端口8848，自动配置目录权限和JVM参数'
);

-- 部署说明
/*
Nacos服务注册中心（自动配置版）说明：

特点：
1. 系统会自动创建挂载目录并设置适当权限
2. 使用standalone模式，适合单机部署
3. 优化的JVM参数配置，适合中小规模使用

部署说明：
1. 系统会自动执行：
   - mkdir -p /data/nacos/logs
   - mkdir -p /data/nacos/conf
   - chmod -R 777 /data/nacos

2. 部署后访问：
   - 管理控制台：http://服务器IP:8848/nacos
   - 默认账号密码：nacos/nacos

3. 常见问题排查：
   a) 容器不断重启：
      - 检查日志：docker logs nacos
      - 可能原因：内存不足、端口冲突、数据库连接问题
   
   b) 无法访问控制台：
      - 检查防火墙是否开放8848端口
      - 检查服务器安全组设置
      - 检查容器网络模式是否正确
   
   c) 启动时间过长：
      - Nacos首次启动可能需要1-2分钟
      - 检查服务器资源使用情况

4. 持久化配置：
   如需使用MySQL存储配置，可添加以下环境变量：
   - SPRING_DATASOURCE_PLATFORM=mysql
   - MYSQL_SERVICE_HOST=mysql主机地址
   - MYSQL_SERVICE_PORT=3306
   - MYSQL_SERVICE_DB_NAME=nacos数据库名
   - MYSQL_SERVICE_USER=nacos数据库用户
   - MYSQL_SERVICE_PASSWORD=密码

5. 集群配置：
   如需配置集群模式，将MODE环境变量改为cluster，并配置集群列表

6. 手动部署命令参考：
   docker run -d --name nacos \
     -p 8848:8848 -p 9848:9848 -p 9849:9849 \
     -v /data/nacos/logs:/home/<USER>/logs \
     -v /data/nacos/conf:/home/<USER>/conf \
     -e MODE=standalone \
     -e PREFER_HOST_MODE=hostname \
     -e JVM_XMS=256m \
     -e JVM_XMX=512m \
     -e JVM_XMN=128m \
     --restart always \
     nacos/nacos-server:latest

7. 故障排查命令：
   - 查看容器状态：docker ps -a | grep nacos
   - 查看容器日志：docker logs nacos
   - 进入容器排查：docker exec -it nacos bash
   - 检查配置文件：docker exec -it nacos cat /home/<USER>/conf/application.properties
   - 检查JVM参数：docker exec -it nacos ps -ef | grep java
*/ 