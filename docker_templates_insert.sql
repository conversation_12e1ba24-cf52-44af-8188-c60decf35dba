-- Docker模板插入脚本
-- 包含常用中间件：Nacos、RabbitMQ、RocketMQ、MongoDB

-- Nacos模板（使用Docker Compose）
INSERT INTO sys_docker_template (
    template_name, template_description, image_name, image_tag, 
    port_mappings, volume_mappings, environment_vars, network_mode, 
    restart_policy, command, is_system, use_compose, compose_config, 
    create_by, create_time, remark
) VALUES (
    'Nacos服务注册中心', 
    'Nacos是阿里巴巴开源的一个更易于构建云原生应用的动态服务发现、配置管理和服务管理平台',
    'nacos/nacos-server', 
    'latest', 
    NULL, 
    NULL, 
    NULL, 
    'bridge', 
    'always', 
    NULL, 
    '1', 
    '1', 
    'version: "3"
services:
  nacos:
    image: nacos/nacos-server:latest
    container_name: nacos
    ports:
      - "8848:8848"
      - "9848:9848"
      - "9849:9849"
    environment:
      - MODE=standalone
      - JVM_XMS=512m
      - JVM_XMX=512m
    volumes:
      - ./nacos/logs:/home/<USER>/logs
      - ./nacos/conf:/home/<USER>/conf
    restart: always
    networks:
      - nacos_net

networks:
  nacos_net:
    driver: bridge', 
    'admin', 
    sysdate(), 
    '单机模式Nacos服务，默认端口8848，用户名/密码：nacos/nacos'
);

-- RabbitMQ模板（使用Docker Compose）
INSERT INTO sys_docker_template (
    template_name, template_description, image_name, image_tag, 
    port_mappings, volume_mappings, environment_vars, network_mode, 
    restart_policy, command, is_system, use_compose, compose_config, 
    create_by, create_time, remark
) VALUES (
    'RabbitMQ消息队列', 
    'RabbitMQ是实现了高级消息队列协议（AMQP）的开源消息代理软件',
    'rabbitmq', 
    'management', 
    NULL, 
    NULL, 
    NULL, 
    'bridge', 
    'always', 
    NULL, 
    '1', 
    '1', 
    'version: "3"
services:
  rabbitmq:
    image: rabbitmq:management
    container_name: rabbitmq
    ports:
      - "5672:5672"
      - "15672:15672"
    environment:
      - RABBITMQ_DEFAULT_USER=admin
      - RABBITMQ_DEFAULT_PASS=admin123
    volumes:
      - ./rabbitmq/data:/var/lib/rabbitmq
      - ./rabbitmq/log:/var/log/rabbitmq
      - ./enabled_plugins:/etc/rabbitmq/enabled_plugins
    restart: always
    networks:
      - rabbitmq_net

networks:
  rabbitmq_net:
    driver: bridge', 
    'admin', 
    sysdate(), 
    'RabbitMQ服务，默认端口5672，管理界面15672，用户名/密码：admin/admin123'
);

-- RocketMQ模板（使用Docker Compose）
INSERT INTO sys_docker_template (
    template_name, template_description, image_name, image_tag, 
    port_mappings, volume_mappings, environment_vars, network_mode, 
    restart_policy, command, is_system, use_compose, compose_config, 
    create_by, create_time, remark
) VALUES (
    'RocketMQ消息队列', 
    'RocketMQ是阿里巴巴开源的分布式消息中间件',
    'apache/rocketmq', 
    'latest', 
    NULL, 
    NULL, 
    NULL, 
    'bridge', 
    'always', 
    NULL, 
    '1', 
    '1', 
    'version: "3"
services:
  namesrv:
    image: apache/rocketmq:4.9.4
    container_name: rmqnamesrv
    ports:
      - "9876:9876"
    command: sh mqnamesrv
    volumes:
      - ./rocketmq/namesrv/logs:/home/<USER>/logs
      - ./rocketmq/namesrv/store:/home/<USER>/store
    networks:
      - rocketmq_net
    restart: always
      
  broker:
    image: apache/rocketmq:4.9.4
    container_name: rmqbroker
    ports:
      - "10909:10909"
      - "10911:10911"
    depends_on:
      - namesrv
    command: sh mqbroker -n namesrv:9876
    volumes:
      - ./rocketmq/broker/logs:/home/<USER>/logs
      - ./rocketmq/broker/store:/home/<USER>/store
      - ./rocketmq/broker/conf/broker.conf:/opt/rocketmq/conf/broker.conf
    environment:
      - JAVA_OPT_EXT=-server -Xms512m -Xmx512m
    networks:
      - rocketmq_net
    restart: always
      
  dashboard:
    image: apacherocketmq/rocketmq-dashboard:latest
    container_name: rmqdashboard
    ports:
      - "8080:8080"
    depends_on:
      - namesrv
    environment:
      - JAVA_OPTS=-Drocketmq.namesrv.addr=namesrv:9876
    networks:
      - rocketmq_net
    restart: always

networks:
  rocketmq_net:
    driver: bridge', 
    'admin', 
    sysdate(), 
    'RocketMQ服务，包含namesrv、broker和管理控制台，默认端口9876，控制台8080'
);

-- MongoDB模板（使用Docker Compose）
INSERT INTO sys_docker_template (
    template_name, template_description, image_name, image_tag, 
    port_mappings, volume_mappings, environment_vars, network_mode, 
    restart_policy, command, is_system, use_compose, compose_config, 
    create_by, create_time, remark
) VALUES (
    'MongoDB数据库', 
    'MongoDB是一个基于分布式文件存储的开源NoSQL数据库系统',
    'mongo', 
    'latest', 
    NULL, 
    NULL, 
    NULL, 
    'bridge', 
    'always', 
    NULL, 
    '1', 
    '1', 
    'version: "3"
services:
  mongodb:
    image: mongo:latest
    container_name: mongodb
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_ROOT_USERNAME=admin
      - MONGO_INITDB_ROOT_PASSWORD=admin123
    volumes:
      - ./mongodb/data:/data/db
      - ./mongodb/config:/data/configdb
    restart: always
    networks:
      - mongodb_net
      
  mongo-express:
    image: mongo-express:latest
    container_name: mongo-express
    ports:
      - "8081:8081"
    environment:
      - ME_CONFIG_MONGODB_ADMINUSERNAME=admin
      - ME_CONFIG_MONGODB_ADMINPASSWORD=admin123
      - ME_CONFIG_MONGODB_SERVER=mongodb
    depends_on:
      - mongodb
    restart: always
    networks:
      - mongodb_net

networks:
  mongodb_net:
    driver: bridge', 
    'admin', 
    sysdate(), 
    'MongoDB数据库，默认端口27017，包含mongo-express管理界面(8081)，用户名/密码：admin/admin123'
);

-- Redis模板（使用Docker Compose）
INSERT INTO sys_docker_template (
    template_name, template_description, image_name, image_tag, 
    port_mappings, volume_mappings, environment_vars, network_mode, 
    restart_policy, command, is_system, use_compose, compose_config, 
    create_by, create_time, remark
) VALUES (
    'Redis缓存服务', 
    'Redis是一个开源的内存数据库，常用作缓存、消息队列和分布式锁',
    'redis', 
    'latest', 
    NULL, 
    NULL, 
    NULL, 
    'bridge', 
    'always', 
    NULL, 
    '1', 
    '1', 
    'version: "3"
services:
  redis:
    image: redis:latest
    container_name: redis
    ports:
      - "6379:6379"
    command: redis-server --requirepass "redis123" --appendonly yes
    volumes:
      - ./redis/data:/data
    restart: always
    networks:
      - redis_net
      
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: redis-commander
    ports:
      - "8081:8081"
    environment:
      - REDIS_HOSTS=local:redis:6379:0:redis123
    depends_on:
      - redis
    restart: always
    networks:
      - redis_net

networks:
  redis_net:
    driver: bridge', 
    'admin', 
    sysdate(), 
    'Redis缓存服务，默认端口6379，密码redis123，包含Redis Commander管理界面(8081)'
);

-- MySQL模板（使用Docker Compose）
INSERT INTO sys_docker_template (
    template_name, template_description, image_name, image_tag, 
    port_mappings, volume_mappings, environment_vars, network_mode, 
    restart_policy, command, is_system, use_compose, compose_config, 
    create_by, create_time, remark
) VALUES (
    'MySQL数据库', 
    'MySQL是世界上最流行的开源关系型数据库管理系统',
    'mysql', 
    '8.0', 
    NULL, 
    NULL, 
    NULL, 
    'bridge', 
    'always', 
    NULL, 
    '1', 
    '1', 
    'version: "3"
services:
  mysql:
    image: mysql:8.0
    container_name: mysql
    ports:
      - "3306:3306"
    environment:
      - MYSQL_ROOT_PASSWORD=mysql123
      - MYSQL_DATABASE=testdb
      - TZ=Asia/Shanghai
    command: --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci
    volumes:
      - ./mysql/data:/var/lib/mysql
      - ./mysql/conf:/etc/mysql/conf.d
      - ./mysql/init:/docker-entrypoint-initdb.d
    restart: always
    networks:
      - mysql_net
      
  phpmyadmin:
    image: phpmyadmin/phpmyadmin:latest
    container_name: phpmyadmin
    ports:
      - "8080:80"
    environment:
      - PMA_HOST=mysql
      - PMA_PORT=3306
      - MYSQL_ROOT_PASSWORD=mysql123
    depends_on:
      - mysql
    restart: always
    networks:
      - mysql_net

networks:
  mysql_net:
    driver: bridge', 
    'admin', 
    sysdate(), 
    'MySQL数据库8.0版本，默认端口3306，root密码mysql123，包含phpMyAdmin管理界面(8080)'
);

-- Elasticsearch模板（使用Docker Compose）
INSERT INTO sys_docker_template (
    template_name, template_description, image_name, image_tag, 
    port_mappings, volume_mappings, environment_vars, network_mode, 
    restart_policy, command, is_system, use_compose, compose_config, 
    create_by, create_time, remark
) VALUES (
    'Elasticsearch搜索引擎', 
    'Elasticsearch是一个分布式、RESTful风格的搜索和数据分析引擎',
    'elasticsearch', 
    '7.17.9', 
    NULL, 
    NULL, 
    NULL, 
    'bridge', 
    'always', 
    NULL, 
    '1', 
    '1', 
    'version: "3"
services:
  elasticsearch:
    image: elasticsearch:7.17.9
    container_name: elasticsearch
    ports:
      - "9200:9200"
      - "9300:9300"
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
      - xpack.security.enabled=false
    volumes:
      - ./elasticsearch/data:/usr/share/elasticsearch/data
    restart: always
    networks:
      - elastic_net
      
  kibana:
    image: kibana:7.17.9
    container_name: kibana
    ports:
      - "5601:5601"
    environment:
      - ELASTICSEARCH_HOSTS=http://elasticsearch:9200
    depends_on:
      - elasticsearch
    restart: always
    networks:
      - elastic_net

networks:
  elastic_net:
    driver: bridge', 
    'admin', 
    sysdate(), 
    'Elasticsearch 7.17.9，默认端口9200，包含Kibana管理界面(5601)'
); 