-- ----------------------------
-- 文件同步日志表
-- ----------------------------
drop table if exists sync_file_sync_log;
create table sync_file_sync_log (
  log_id            bigint(20)      not null auto_increment    comment '日志ID',
  task_id           bigint(20)      not null                   comment '任务ID',
  task_name         varchar(100)    not null                   comment '任务名称',
  source_server_id  bigint(20)      not null                   comment '源服务器ID',
  source_server_name varchar(100)   not null                   comment '源服务器名称',
  target_server_id  bigint(20)      not null                   comment '目标服务器ID',
  target_server_name varchar(100)   not null                   comment '目标服务器名称',
  sync_type         char(1)         default '1'                comment '同步类型（1全量同步 2增量同步）',
  start_time        datetime                                   comment '开始时间',
  end_time          datetime                                   comment '结束时间',
  duration          int                                        comment '耗时(秒)',
  file_count        int             default 0                  comment '文件数量',
  total_size        bigint(20)      default 0                  comment '总大小(字节)',
  status            char(1)         default '0'                comment '状态（0成功 1失败）',
  error_info        varchar(2000)                              comment '错误信息',
  create_by         varchar(64)     default ''                 comment '创建者',
  create_time       datetime                                   comment '创建时间',
  remark            varchar(500)    default null               comment '备注',
  primary key (log_id)
) engine=innodb auto_increment=100 comment = '文件同步日志表'; 