package com.ruoyi.system.mapper;

import java.util.List;
import com.ruoyi.system.domain.datasync.SyncServer;

/**
 * 服务器Mapper接口
 * 
 * <AUTHOR>
 */
public interface SyncServerMapper 
{
    /**
     * 查询服务器
     * 
     * @param serverId 服务器主键
     * @return 服务器
     */
    public SyncServer selectSyncServerByServerId(Long serverId);

    /**
     * 查询服务器列表
     * 
     * @param syncServer 服务器
     * @return 服务器集合
     */
    public List<SyncServer> selectSyncServerList(SyncServer syncServer);

    /**
     * 新增服务器
     * 
     * @param syncServer 服务器
     * @return 结果
     */
    public int insertSyncServer(SyncServer syncServer);

    /**
     * 修改服务器
     * 
     * @param syncServer 服务器
     * @return 结果
     */
    public int updateSyncServer(SyncServer syncServer);

    /**
     * 删除服务器
     * 
     * @param serverId 服务器主键
     * @return 结果
     */
    public int deleteSyncServerByServerId(Long serverId);

    /**
     * 批量删除服务器
     * 
     * @param serverIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSyncServerByServerIds(Long[] serverIds);
} 