# 数据库同步定时任务功能

## 功能概述

数据库同步定时任务功能允许用户配置自动执行的数据库同步任务，支持按日、按周、按月定时执行同步操作，大大减轻了手动操作的工作量。

## 新增功能

1. 任务调度功能：支持设置同步任务的执行周期（手动、每天、每周、每月）和执行时间
2. 自动计算下次执行时间：系统会根据执行周期和执行时间自动计算下次执行时间
3. 自动检测并执行到期任务：系统定时检查是否有到期的同步任务，自动执行需要运行的任务
4. 任务执行记录：记录任务执行时间、状态和结果，方便用户查询历史记录

## 技术实现

1. 定时任务实现：使用Quartz定时任务框架，每5分钟检查一次是否有到期的同步任务
2. 同步任务识别：通过数据库中的`exec_cycle`和`exec_time`字段识别需要自动执行的任务
3. 下次执行时间计算：根据当前时间和执行周期自动计算下次执行时间（`next_exec_time`）
4. 执行结果记录：在同步日志表中记录每次执行的结果和详细信息

## 使用说明

### 配置定时同步任务

1. 在"数据同步 > 数据库同步"页面，点击"新增"或"修改"按钮
2. 填写基本信息：同步任务名称、源数据库、目标数据库、同步表等
3. 设置同步方式：选择"全量同步"或"增量同步"
4. 设置执行周期：
   - 手动：仅通过手动触发执行
   - 每天：每天在指定时间执行
   - 每周：每周一在指定时间执行
   - 每月：每月1号在指定时间执行
5. 设置执行时间：格式为"HH:mm"，例如"08:30"表示早上8点30分
6. 点击"确定"保存任务配置

### 查看任务执行情况

1. 在数据库同步列表中可以查看每个任务的执行情况
2. "最近执行时间"列显示上次执行的时间
3. "下次执行时间"列显示计划中的下次执行时间
4. 点击"日志"按钮可查看详细的执行记录和结果

## 数据库结构

同步任务表（sync_db_sync）关键字段：

| 字段名 | 类型 | 说明 |
| ------ | ------ | ------ |
| sync_id | bigint | 同步ID |
| sync_name | varchar | 同步任务名称 |
| sync_type | char | 同步类型（0手动同步 1自动同步）|
| sync_mode | char | 同步方式（1全量同步 2增量同步）|
| exec_cycle | char | 执行周期（0手动 1每天 2每周 3每月）|
| exec_time | varchar | 执行时间（HH:mm格式）|
| last_exec_time | varchar | 最近执行时间 |
| next_exec_time | varchar | 下次执行时间 |
| status | char | 状态（0正常 1停用）|

## 部署说明

1. 执行数据库脚本：
   ```sql
   -- 添加数据库同步定时任务
   INSERT INTO sys_job (job_id, job_name, job_group, invoke_target, cron_expression, misfire_policy, concurrent, status, create_by, create_time, remark) 
   VALUES (5, '数据库同步定时任务', 'DEFAULT', 'syncDbTask.executeScheduledTasks()', '0 0/5 * * * ?', '3', '1', '0', 'admin', SYSDATE(), '每5分钟执行一次，检查并执行到期的数据库同步任务');
   ```

2. 重启应用服务器，使定时任务生效

## 注意事项

1. 全量同步会清空目标表数据，请谨慎使用
2. 增量同步需要表有主键，否则将退化为全量同步
3. 执行周期为"手动"的任务不会自动执行，需要手动点击执行按钮
4. 当任务状态为"停用"时，自动任务不会执行，但仍可手动执行
5. 修改任务的执行周期或执行时间后，系统会自动重新计算下次执行时间 