-- ----------------------------
-- 数据库类型字典数据
-- ----------------------------
-- 添加字典类型
insert into sys_dict_type(dict_id, dict_name, dict_type, status, create_by, create_time, update_by, update_time, remark)
values(100, '数据库类型', 'sys_db_type', '0', 'admin', sysdate(), '', null, '数据库类型列表');

-- 查询现有数据库类型字典
-- SELECT * FROM sys_dict_data WHERE dict_type = 'sys_db_type';

-- 删除已有数据，重新插入
DELETE FROM sys_dict_data WHERE dict_type = 'sys_db_type';

-- 添加常见数据库类型
INSERT INTO sys_dict_data(dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) 
VALUES (1, 'MySQL', 'mysql', 'sys_db_type', '', 'primary', 'Y', '0', 'admin', sysdate(), '', NULL, 'MySQL数据库');

INSERT INTO sys_dict_data(dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) 
VALUES (2, 'Oracle', 'oracle', 'sys_db_type', '', 'success', 'N', '0', 'admin', sysdate(), '', NULL, 'Oracle数据库');

INSERT INTO sys_dict_data(dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) 
VALUES (3, 'SQL Server', 'sqlserver', 'sys_db_type', '', 'info', 'N', '0', 'admin', sysdate(), '', NULL, 'SQL Server数据库');

INSERT INTO sys_dict_data(dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) 
VALUES (4, 'PostgreSQL', 'postgresql', 'sys_db_type', '', 'warning', 'N', '0', 'admin', sysdate(), '', NULL, 'PostgreSQL数据库');

INSERT INTO sys_dict_data(dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) 
VALUES (5, 'MariaDB', 'mariadb', 'sys_db_type', '', 'primary', 'N', '0', 'admin', sysdate(), '', NULL, 'MariaDB数据库');

INSERT INTO sys_dict_data(dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) 
VALUES (6, 'DB2', 'db2', 'sys_db_type', '', 'danger', 'N', '0', 'admin', sysdate(), '', NULL, 'IBM DB2数据库');

INSERT INTO sys_dict_data(dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) 
VALUES (7, 'SQLite', 'sqlite', 'sys_db_type', '', 'info', 'N', '0', 'admin', sysdate(), '', NULL, 'SQLite数据库');

INSERT INTO sys_dict_data(dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) 
VALUES (8, 'Sybase', 'sybase', 'sys_db_type', '', 'warning', 'N', '0', 'admin', sysdate(), '', NULL, 'Sybase数据库');

INSERT INTO sys_dict_data(dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) 
VALUES (9, 'MongoDB', 'mongodb', 'sys_db_type', '', 'success', 'N', '0', 'admin', sysdate(), '', NULL, 'MongoDB数据库');

INSERT INTO sys_dict_data(dict_sort, dict_label, dict_value, dict_type, css_class, list_class, is_default, status, create_by, create_time, update_by, update_time, remark) 
VALUES (10, 'Redis', 'redis', 'sys_db_type', '', 'danger', 'N', '0', 'admin', sysdate(), '', NULL, 'Redis数据库'); 