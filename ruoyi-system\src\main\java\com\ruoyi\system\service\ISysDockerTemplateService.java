package com.ruoyi.system.service;

import java.util.List;
import java.util.Map;
import com.ruoyi.system.domain.SysDockerTemplate;

/**
 * Docker容器模板Service接口
 * 
 * <AUTHOR>
 */
public interface ISysDockerTemplateService 
{
    /**
     * 查询Docker容器模板
     * 
     * @param templateId Docker容器模板ID
     * @return Docker容器模板
     */
    public SysDockerTemplate selectDockerTemplateById(Long templateId);

    /**
     * 查询Docker容器模板列表
     * 
     * @param dockerTemplate Docker容器模板
     * @return Docker容器模板集合
     */
    public List<SysDockerTemplate> selectDockerTemplateList(SysDockerTemplate dockerTemplate);

    /**
     * 新增Docker容器模板
     * 
     * @param dockerTemplate Docker容器模板
     * @return 结果
     */
    public int insertDockerTemplate(SysDockerTemplate dockerTemplate);

    /**
     * 修改Docker容器模板
     * 
     * @param dockerTemplate Docker容器模板
     * @return 结果
     */
    public int updateDockerTemplate(SysDockerTemplate dockerTemplate);

    /**
     * 批量删除Docker容器模板
     * 
     * @param templateIds 需要删除的Docker容器模板ID
     * @return 结果
     */
    public int deleteDockerTemplateByIds(Long[] templateIds);

    /**
     * 删除Docker容器模板信息
     * 
     * @param templateId Docker容器模板ID
     * @return 结果
     */
    public int deleteDockerTemplateById(Long templateId);
    
    /**
     * 从模板创建Docker容器
     * 
     * @param serverId 服务器ID
     * @param templateId 模板ID
     * @param containerName 容器名称
     * @param customParams 自定义参数
     * @return 结果
     */
    public Map<String, Object> createContainerFromTemplate(Long serverId, Long templateId, String containerName, Map<String, String> customParams);
} 